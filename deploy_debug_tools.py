#!/usr/bin/env python3
"""
部署Debug工具到Django项目
将API测试工具和前端测试页面集成到开发服务器
"""

import os
import shutil
import sys
from pathlib import Path

def main():
    print("🚀 部署Debug工具到Django项目")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    debug_dir = project_root / 'debug'
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 Debug目录: {debug_dir}")
    
    # 检查必要的源文件
    required_sources = [
        'api-tester',
        'frontend-test'
    ]
    
    missing_sources = []
    for source in required_sources:
        if not (project_root / source).exists():
            missing_sources.append(source)
    
    if missing_sources:
        print("❌ 缺少必要的源文件:")
        for source in missing_sources:
            print(f"   - {source}")
        print("\n请确保api-tester和frontend-test目录存在")
        sys.exit(1)
    
    # 创建debug目录结构
    print("\n📂 创建debug目录结构...")
    debug_dir.mkdir(exist_ok=True)
    (debug_dir / 'templates' / 'debug').mkdir(parents=True, exist_ok=True)
    
    # 复制API测试工具
    print("📋 复制API测试工具...")
    api_tester_dest = debug_dir / 'api-tester'
    if api_tester_dest.exists():
        shutil.rmtree(api_tester_dest)
    shutil.copytree(project_root / 'api-tester', api_tester_dest)
    
    # 重命名手动测试工具文件
    manual_tester_src = api_tester_dest / 'manual-tester.html'
    manual_tester_dest = api_tester_dest / 'manual.html'
    if manual_tester_src.exists():
        manual_tester_src.rename(manual_tester_dest)
        print("✅ 重命名 manual-tester.html -> manual.html")
    
    # 复制前端测试页面
    print("📱 复制前端测试页面...")
    frontend_test_dest = debug_dir / 'frontend-test'
    if frontend_test_dest.exists():
        shutil.rmtree(frontend_test_dest)
    shutil.copytree(project_root / 'frontend-test', frontend_test_dest)
    
    # 更新环境配置
    print("⚙️ 更新环境配置...")
    update_environment_config(api_tester_dest / 'config' / 'environment.json')
    
    # 检查Django配置
    print("🔧 检查Django配置...")
    check_django_config(project_root)
    
    print("\n✅ Debug工具部署完成！")
    print("\n🌐 访问地址:")
    print("  本地开发: http://localhost:8000/debug/")
    print("  开发服务器: https://pqqjrlgoetaa.usw.sealos.io/debug/")
    print("\n📋 可用工具:")
    print("  - API测试工具（自动）: /debug/api-tester/")
    print("  - API测试工具（手动）: /debug/api-tester/manual/")
    print("  - 前端测试页面: /debug/frontend-test/")
    
    print("\n🚀 启动Django服务器:")
    print("  python manage.py runserver")

def update_environment_config(config_path):
    """更新环境配置文件"""
    import json
    
    if not config_path.exists():
        print(f"⚠️  环境配置文件不存在: {config_path}")
        return
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新开发环境配置
        if 'environments' in config and 'development' in config['environments']:
            dev_config = config['environments']['development']
            dev_config['testServerPort'] = 8080
            dev_config['googleOAuth']['redirectUri'] = 'https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/'
        
        # 更新本地环境配置
        if 'environments' in config and 'local' in config['environments']:
            local_config = config['environments']['local']
            local_config['apiBaseUrl'] = 'http://localhost:8000'
            local_config['testServerPort'] = 8000
            local_config['googleOAuth']['redirectUri'] = 'http://localhost:8000/debug/api-tester/'
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 环境配置已更新")
        
    except Exception as e:
        print(f"⚠️  更新环境配置失败: {e}")

def check_django_config(project_root):
    """检查Django配置"""
    urls_file = project_root / 'mokta' / 'urls.py'
    
    if not urls_file.exists():
        print("⚠️  Django URLs文件不存在")
        return
    
    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'debug.urls' in content:
            print("✅ Django URLs配置已包含debug路由")
        else:
            print("⚠️  Django URLs配置缺少debug路由")
            print("请在mokta/urls.py中添加:")
            print("  path('debug/', include('debug.urls')),")
        
        if 'from django.conf import settings' in content:
            print("✅ Django settings导入正常")
        else:
            print("⚠️  请在mokta/urls.py中添加:")
            print("  from django.conf import settings")
            
    except Exception as e:
        print(f"⚠️  检查Django配置失败: {e}")

def create_debug_app():
    """创建debug Django应用"""
    debug_dir = Path(__file__).parent / 'debug'
    
    # 创建__init__.py
    init_file = debug_dir / '__init__.py'
    if not init_file.exists():
        init_file.touch()
        print("✅ 创建 debug/__init__.py")
    
    # 创建apps.py
    apps_file = debug_dir / 'apps.py'
    if not apps_file.exists():
        apps_content = '''from django.apps import AppConfig

class DebugConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'debug'
'''
        with open(apps_file, 'w', encoding='utf-8') as f:
            f.write(apps_content)
        print("✅ 创建 debug/apps.py")

if __name__ == "__main__":
    main()
    create_debug_app()
