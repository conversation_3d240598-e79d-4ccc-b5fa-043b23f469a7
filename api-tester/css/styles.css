/* Mokta API 测试程序样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 区域样式 */
section {
    background: white;
    margin-bottom: 20px;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* 状态样式 */
.status {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
}

.status.info {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.status.success {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.status.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.status.warning {
    background-color: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffcc02;
}

/* 环境信息样式 */
.environment-section {
    margin-bottom: 15px;
}

.env-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    border-left: 4px solid #007bff;
}

.env-info h4 {
    color: #495057;
    margin-bottom: 15px;
}

.env-info p {
    margin-bottom: 8px;
    color: #6c757d;
}

.local-instructions {
    background-color: #fff3cd;
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #ffeaa7;
}

.local-instructions h5 {
    color: #856404;
    margin-bottom: 10px;
}

.local-instructions ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.local-instructions li {
    color: #856404;
    margin-bottom: 5px;
}

/* 模拟登录样式 */
.mock-login {
    text-align: center;
    padding: 20px;
    background-color: #e3f2fd;
    border-radius: 8px;
    border: 1px solid #bbdefb;
}

.mock-note {
    font-size: 14px;
    color: #1976d2;
    margin-top: 10px;
    font-style: italic;
}

/* 用户信息样式 */
.user-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.user-info h3 {
    color: #28a745;
    margin-bottom: 15px;
}

.user-info p {
    margin-bottom: 10px;
}

/* 表单控件样式 */
.control-group {
    margin-bottom: 20px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #1e7e34;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
    transform: translateY(-1px);
}

.control-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

/* 端点详情样式 */
.endpoint-details {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #dee2e6;
}

.endpoint-details h4 {
    color: #495057;
    margin-bottom: 15px;
}

.endpoint-details h5 {
    color: #6c757d;
    margin: 15px 0 10px 0;
}

.endpoint-details pre {
    background-color: #e9ecef;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 13px;
    border: 1px solid #ced4da;
}

/* 测试结果样式 */
.test-results {
    max-height: 600px;
    overflow-y: auto;
}

.test-result {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.test-result.success {
    border-left: 4px solid #28a745;
}

.test-result.failure {
    border-left: 4px solid #dc3545;
}

.result-header {
    background-color: #f8f9fa;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 500;
}

.result-status {
    font-size: 18px;
}

.result-title {
    flex: 1;
    font-family: 'Courier New', monospace;
    color: #495057;
}

.result-time {
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;
}

.result-code {
    background-color: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.result-description {
    padding: 10px 15px;
    color: #6c757d;
    font-style: italic;
}

.result-details {
    padding: 15px;
    background-color: #ffffff;
}

.result-timestamp {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 10px;
}

.result-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    border: 1px solid #f5c6cb;
}

.result-response {
    margin-top: 10px;
}

.result-response summary {
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    padding: 8px 0;
}

.result-response pre {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    border: 1px solid #dee2e6;
    margin-top: 10px;
}

/* 无结果样式 */
.no-results {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.no-results .hint {
    font-size: 14px;
    margin-top: 10px;
}

/* 帮助区域样式 */
.help-section {
    background-color: #f8f9fa;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.step-number {
    background-color: #007bff;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content h4 {
    color: #495057;
    margin-bottom: 8px;
}

.step-content p {
    color: #6c757d;
    line-height: 1.5;
}

.features, .api-info {
    margin-top: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.features h3, .api-info h3 {
    color: #495057;
    margin-bottom: 15px;
}

.features ul, .api-info ul {
    list-style-type: none;
    padding-left: 0;
}

.features li, .api-info li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.features li:last-child, .api-info li:last-child {
    border-bottom: none;
}

.api-info code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

/* 页脚样式 */
footer {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin: 5px 0;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin: 0 auto 15px auto;
    }
}
