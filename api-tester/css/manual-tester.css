/* 手动API测试工具专用样式 */

/* Token显示和管理 */
.token-display {
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    word-break: break-all;
    max-width: 400px;
    display: inline-block;
    border: 1px solid #dee2e6;
}

.manual-token-section {
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.token-input-group {
    display: flex;
    gap: 10px;
    margin: 15px 0;
    align-items: center;
}

.token-input-group input {
    flex: 1;
}

.token-note {
    font-size: 14px;
    color: #6c757d;
    margin: 10px 0 0 0;
    font-style: italic;
}

/* 预设API选择 */
.preset-apis {
    background-color: #e3f2fd;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border: 1px solid #bbdefb;
}

.preset-apis h3 {
    margin-bottom: 15px;
    color: #1976d2;
}

.preset-apis select {
    margin-right: 10px;
    flex: 1;
}

.preset-apis > div {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 请求构建器 */
.request-builder {
    margin-bottom: 25px;
}

.request-line {
    display: flex;
    gap: 10px;
    align-items: center;
    background-color: #ffffff;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.method-selector {
    min-width: 100px;
    font-weight: bold;
    color: #007bff;
}

.url-input {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.send-btn {
    min-width: 120px;
    font-weight: bold;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.send-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* 选项卡样式 */
.request-tabs {
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    overflow: hidden;
    margin-bottom: 25px;
}

.tab-headers {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-header {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
}

.tab-header:hover {
    background-color: #e9ecef;
    color: #495057;
}

.tab-header.active {
    background-color: #007bff;
    color: white;
    border-bottom: 2px solid #0056b3;
}

.tab-content {
    display: none;
    padding: 25px;
}

.tab-content.active {
    display: block;
}

.tab-content h4 {
    margin-bottom: 20px;
    color: #495057;
}

/* 键值对输入容器 */
.key-value-container {
    margin-bottom: 15px;
}

.key-value-pair {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.key-value-pair input {
    flex: 1;
}

.key-value-pair .key-input {
    max-width: 200px;
}

.key-value-pair .remove-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.key-value-pair .remove-btn:hover {
    background-color: #c82333;
}

/* 请求体样式 */
.body-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.body-type-selector label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-weight: 500;
}

.body-container {
    margin-top: 15px;
}

.json-input, .text-input {
    width: 100%;
    min-height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    padding: 15px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    resize: vertical;
}

.json-input:focus, .text-input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 认证选项 */
.auth-options {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.auth-options label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-weight: 500;
}

.auth-container {
    margin-top: 15px;
}

.auth-container input {
    margin-bottom: 10px;
}

/* 配置管理 */
.config-management {
    background-color: #fff3cd;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
    margin-bottom: 25px;
}

.config-management h4 {
    color: #856404;
    margin-bottom: 15px;
}

.config-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.config-controls input,
.config-controls select {
    min-width: 150px;
}

/* 响应结果样式 */
.response-container {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    min-height: 300px;
}

.no-response {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6c757d;
    font-style: italic;
}

.response-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.response-status {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
}

.response-status.success {
    background-color: #28a745;
}

.response-status.error {
    background-color: #dc3545;
}

.response-time {
    color: #6c757d;
    font-size: 14px;
}

.response-body {
    padding: 20px;
}

.response-json {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    overflow-x: auto;
    border: 1px solid #e9ecef;
}

/* 历史记录样式 */
.history-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.history-container {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.no-history {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
    font-style: italic;
}

.history-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.history-item:hover {
    background-color: #f8f9fa;
}

.history-item:last-child {
    border-bottom: none;
}

.history-method {
    display: inline-block;
    min-width: 60px;
    font-weight: bold;
    color: #007bff;
}

.history-url {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #495057;
    margin-left: 10px;
}

.history-time {
    font-size: 12px;
    color: #6c757d;
    float: right;
}

.history-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 10px;
}

.history-status.success {
    background-color: #d4edda;
    color: #155724;
}

.history-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .request-line {
        flex-direction: column;
        gap: 15px;
    }
    
    .method-selector,
    .url-input,
    .send-btn {
        width: 100%;
    }
    
    .tab-headers {
        flex-direction: column;
    }
    
    .key-value-pair {
        flex-direction: column;
        gap: 10px;
    }
    
    .config-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .config-controls input,
    .config-controls select,
    .config-controls button {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .body-type-selector,
    .auth-options {
        flex-direction: column;
        gap: 10px;
    }
}
