#!/usr/bin/env python3
"""
Mokta API 测试程序启动脚本
启动本地HTTP服务器用于运行API测试程序
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def find_available_port(start_port=3000):
    """查找可用端口，避免与后端服务冲突"""
    import socket

    # 避免常用的后端端口：8000, 8080, 8888, 5000等
    avoid_ports = [8000, 8080, 8888, 5000, 3000]

    for port in range(start_port, start_port + 100):
        if port in avoid_ports:
            continue

        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue

    # 如果都不可用，返回默认端口
    return 3001

def main():
    # 自动查找可用端口
    PORT = find_available_port()
    
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent.absolute()
    
    # 切换到API测试程序目录
    os.chdir(current_dir)
    
    print("🧪 Mokta API 测试程序启动器")
    print("=" * 50)
    print(f"📁 工作目录: {current_dir}")
    print(f"🌐 端口: {PORT}")
    print(f"🔗 访问地址: http://localhost:{PORT}")
    print("=" * 50)
    
    # 检查必要文件是否存在
    required_files = [
        'index.html',
        'js/api-tester.js',
        'css/styles.css',
        'config/api-endpoints.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n请确保所有文件都已正确创建。")
        sys.exit(1)
    
    print("✅ 所有必要文件检查完成")
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    # 添加CORS支持
    class CORSRequestHandler(Handler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            super().end_headers()
        
        def do_OPTIONS(self):
            self.send_response(200)
            self.end_headers()
    
    try:
        with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
            print(f"🚀 服务器启动成功！")
            print(f"📱 请在浏览器中访问: http://localhost:{PORT}")
            print("🛑 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 已自动打开浏览器")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print("请手动在浏览器中访问上述地址")
            
            print("\n📋 使用说明:")
            print("1. 点击 Google 登录按钮进行身份验证")
            print("2. 选择要测试的 API 端点")
            print("3. 点击测试按钮执行测试")
            print("4. 查看测试结果和详细信息")
            print("5. 可以导出测试结果为 JSON 文件")
            print("=" * 50)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试以下解决方案:")
            print("1. 关闭占用该端口的其他程序")
            print("2. 修改脚本中的 PORT 变量使用其他端口")
            print("3. 等待几分钟后重试")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
