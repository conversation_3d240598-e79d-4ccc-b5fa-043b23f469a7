{"baseUrl": "https://pqqjrlgoetaa.usw.sealos.io", "apiVersion": "v1", "endpoints": {"authentication": {"google_login": {"method": "POST", "path": "/api/v1/login/google", "requiresAuth": false, "description": "Google OAuth 登录", "requestBody": {"id_token": "string"}, "expectedResponse": {"access_token": "string", "user": {"id": "uuid", "email": "string", "nickname": "string", "avatar_url": "string"}}, "testData": {"valid": {"id_token": "GOOGLE_ID_TOKEN_PLACEHOLDER"}, "invalid": {"id_token": "invalid_token"}}}, "apple_login": {"method": "POST", "path": "/api/v1/login/apple", "requiresAuth": false, "description": "Apple OAuth 登录", "requestBody": {"id_token": "string"}, "expectedResponse": {"access_token": "string", "user": {"id": "uuid", "email": "string", "nickname": "string", "avatar_url": "string"}}, "testData": {"valid": {"id_token": "APPLE_ID_TOKEN_PLACEHOLDER"}, "invalid": {"id_token": "invalid_token"}}}}, "users": {"get_profile": {"method": "GET", "path": "/api/v1/users/me", "requiresAuth": true, "description": "获取用户信息", "expectedResponse": {"id": "uuid", "email": "string", "nickname": "string", "avatar_url": "string", "created_at": "datetime", "updated_at": "datetime"}}, "update_profile": {"method": "PUT", "path": "/api/v1/users/me", "requiresAuth": true, "description": "更新用户信息", "requestBody": {"nickname": "string", "avatar_url": "string"}, "expectedResponse": {"id": "uuid", "email": "string", "nickname": "string", "avatar_url": "string", "updated_at": "datetime"}, "testData": {"valid": {"nickname": "测试昵称", "avatar_url": "https://example.com/avatar.jpg"}}}, "delete_account": {"method": "DELETE", "path": "/api/v1/users/me/delete", "requiresAuth": true, "description": "删除用户账号（软删除）", "requestBody": {"confirm_delete": true}, "expectedResponse": {"message": "Account deleted successfully", "deleted_at": "datetime"}, "testData": {"valid": {"confirm_delete": true}, "invalid": {"confirm_delete": false}}}, "get_today_usage": {"method": "GET", "path": "/api/v1/users/me/usage/today", "requiresAuth": true, "description": "获取今日使用情况", "expectedResponse": {"date": "string", "total_generations": "integer", "daily_limit": "integer", "remaining": "integer", "can_generate": "boolean", "is_admin": "boolean"}}}, "uploads": {"get_presigned_url": {"method": "POST", "path": "/api/v1/upload/presigned-url", "requiresAuth": true, "description": "获取图片上传预签名URL", "requestBody": {"file_name": "string", "file_type": "string", "file_size": "integer"}, "expectedResponse": {"upload_url": "string", "file_key": "string", "expires_at": "datetime"}, "testData": {"valid": {"file_name": "test-image.jpg", "file_type": "image/jpeg", "file_size": 1024000}, "invalid_type": {"file_name": "test.txt", "file_type": "text/plain", "file_size": 1024}, "too_large": {"file_name": "large-image.jpg", "file_type": "image/jpeg", "file_size": 11000000}}}}, "characters": {"create_generation_task": {"method": "POST", "path": "/api/v1/characters/generate", "requiresAuth": true, "description": "创建角色生成任务", "requestBody": {"source_photo_key": "string", "quality": "string"}, "expectedResponse": {"task_id": "uuid", "status": "PENDING", "message": "3D character generation task created", "quality": "string"}, "testData": {"valid": {"source_photo_key": "test-photo-key", "quality": "high"}, "different_quality": {"source_photo_key": "test-photo-key", "quality": "medium"}}}, "get_user_characters": {"method": "GET", "path": "/api/v1/characters/", "requiresAuth": true, "description": "获取用户角色列表", "queryParams": {"page": "integer", "page_size": "integer"}, "expectedResponse": {"total": "integer", "page": "integer", "page_size": "integer", "total_pages": "integer", "characters": "array"}}, "get_character_detail": {"method": "GET", "path": "/api/v1/characters/{character_id}", "requiresAuth": true, "description": "获取角色详情", "pathParams": {"character_id": "uuid"}, "expectedResponse": {"id": "uuid", "name": "string", "model_url": "string", "thumbnail_url": "string", "metadata": "object", "created_at": "datetime"}}, "update_character": {"method": "PUT", "path": "/api/v1/characters/{character_id}", "requiresAuth": true, "description": "更新角色", "pathParams": {"character_id": "uuid"}, "requestBody": {"name": "string", "metadata": "object"}, "expectedResponse": {"id": "uuid", "name": "string", "model_url": "string", "thumbnail_url": "string", "metadata": "object", "updated_at": "datetime"}, "testData": {"valid": {"name": "更新后的角色名称", "metadata": {"test": "data"}}}}, "delete_character": {"method": "DELETE", "path": "/api/v1/characters/{character_id}", "requiresAuth": true, "description": "删除角色（软删除）", "pathParams": {"character_id": "uuid"}, "expectedResponse": {"message": "Character deleted successfully", "deleted_at": "datetime"}}}, "tasks": {"get_task_status": {"method": "GET", "path": "/api/v1/tasks/{task_id}/status", "requiresAuth": true, "description": "查询任务状态", "pathParams": {"task_id": "uuid"}, "expectedResponse": {"task_id": "uuid", "status": "string", "progress": "integer", "message": "string"}}}, "motions": {"get_all_motions": {"method": "GET", "path": "/api/v1/motions/", "requiresAuth": true, "description": "获取所有动作列表", "queryParams": {"page": "integer", "page_size": "integer"}, "expectedResponse": {"total": "integer", "page": "integer", "page_size": "integer", "total_pages": "integer", "motions": "array"}}}}, "commonErrors": {"401": {"error": "unauthorized", "message": "Invalid access token"}, "403": {"error": "forbidden", "message": "You don't have permission to access this resource"}, "404": {"error": "not_found", "message": "The requested resource does not exist"}, "429": {"error": "rate_limit_exceeded", "message": "Too many requests, please try again later"}, "500": {"error": "internal_server_error", "message": "Internal server error"}}}