{"environments": {"local": {"name": "本地开发环境", "apiBaseUrl": "http://localhost:8000", "testServerPort": 3001, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com", "note": "注意：此Client ID配置的redirect URI可能不支持localhost，需要配置本地域名"}, "features": {"autoLogin": false, "mockAuth": true, "skipAuthEndpoints": true}}, "development": {"name": "开发服务器环境", "apiBaseUrl": "https://pqqjrlgoetaa.usw.sealos.io", "testServerPort": 3002, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com"}, "features": {"autoLogin": true, "mockAuth": false, "skipAuthEndpoints": false}}, "production": {"name": "生产环境", "apiBaseUrl": "https://pqqjrlgoetaa.usw.sealos.io", "testServerPort": 3003, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com"}, "features": {"autoLogin": true, "mockAuth": false, "skipAuthEndpoints": false}}}, "defaultEnvironment": "local", "localDevelopmentOptions": {"mockJwtToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiNTUwZTg0MDAtZTI5Yi00MWQ0LWE3MTYtNDQ2NjU1NDQwMDAwIiwiZXhwIjoxNzM2NTUzNjAwfQ.mock_signature_for_testing", "mockUser": {"id": "550e8400-e29b-41d4-a716-446655440000", "email": "<EMAIL>", "nickname": "API测试用户", "avatar_url": "https://via.placeholder.com/150"}, "instructions": ["本地开发模式说明：", "1. 由于Google OAuth的redirect URI限制，本地环境将使用模拟登录", "2. 模拟登录会提供一个测试JWT Token用于API测试", "3. 如需真实OAuth登录，请：", "   - 在Google Cloud Console中添加 http://localhost:3001 到授权重定向URI", "   - 或使用开发/生产环境进行测试", "4. 本地模式会自动跳过需要真实OAuth的认证端点测试"]}}