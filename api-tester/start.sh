#!/bin/bash

# Mokta API 测试程序启动脚本
# 支持 macOS 和 Linux

echo "🧪 Mokta API 测试程序启动器"
echo "=================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装，请先安装 Python 3"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 工作目录: $SCRIPT_DIR"
echo "🌐 端口: 8080"
echo "🔗 访问地址: http://localhost:8080"
echo "=================================="

# 检查必要文件
required_files=("index.html" "js/api-tester.js" "css/styles.css" "config/api-endpoints.json")
missing_files=()

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo "❌ 缺少必要文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "请确保所有文件都已正确创建。"
    exit 1
fi

echo "✅ 所有必要文件检查完成"

# 尝试使用Python启动脚本
if [[ -f "start-server.py" ]]; then
    echo "🚀 使用 Python 启动脚本..."
    python3 start-server.py
else
    echo "🚀 使用 Python 内置服务器..."
    echo "📱 请在浏览器中访问: http://localhost:8080"
    echo "🛑 按 Ctrl+C 停止服务器"
    echo "=================================="
    
    # 尝试自动打开浏览器
    if command -v open &> /dev/null; then
        # macOS
        open "http://localhost:8080"
        echo "🌐 已自动打开浏览器 (macOS)"
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "http://localhost:8080"
        echo "🌐 已自动打开浏览器 (Linux)"
    else
        echo "⚠️  请手动在浏览器中访问: http://localhost:8080"
    fi
    
    # 启动Python内置服务器
    python3 -m http.server 8080
fi
