# 🛠️ Mokta API 手动测试工具使用指南

## 🚀 快速开始

### 启动手动测试工具
```bash
cd api-tester
python3 start-server.py manual
```

程序会自动打开浏览器访问: `http://localhost:3001/manual-tester.html`

## 🎯 主要功能

### 1. 🔐 认证管理
- **Google OAuth登录**: 点击Google登录按钮获取真实JWT Token
- **手动Token输入**: 可以粘贴从其他工具获取的Token
- **Token复制**: 一键复制当前Token到剪贴板
- **自动Token应用**: 登录后自动应用到Bearer认证

### 2. 🛠️ API请求构建器

#### 基本请求设置
- **HTTP方法**: GET, POST, PUT, DELETE, PATCH, OPTIONS
- **URL输入**: 支持完整URL或相对路径
- **一键发送**: 大按钮发送请求

#### 预设API端点
- 从下拉菜单选择Mokta的预设API
- 自动填充方法、URL、Headers、请求体
- 基于您的API文档生成

#### Headers管理
- 动态添加/删除Header键值对
- 默认包含Content-Type
- 认证Header自动管理

#### 查询参数
- 动态添加/删除查询参数
- 自动拼接到URL

#### 请求体支持
- **JSON**: 语法高亮，格式化功能
- **Form Data**: 键值对表单数据
- **Raw Text**: 原始文本内容
- **None**: 无请求体

#### 认证选项
- **No Auth**: 无认证
- **Bearer Token**: JWT Token认证
- **Basic Auth**: 用户名密码认证

### 3. 📊 响应结果展示
- **状态码**: 彩色显示成功/失败状态
- **响应时间**: 毫秒级精度
- **响应头**: 完整的HTTP响应头
- **响应体**: JSON格式化显示
- **一键复制**: 复制响应内容

### 4. 💾 配置管理
- **保存配置**: 保存当前请求配置
- **加载配置**: 快速加载已保存的配置
- **删除配置**: 管理不需要的配置
- **本地存储**: 配置保存在浏览器本地

### 5. 📜 请求历史
- **自动记录**: 所有请求自动保存到历史
- **点击加载**: 点击历史记录快速重新发送
- **历史导出**: 导出历史记录为JSON文件
- **历史清除**: 一键清除所有历史

## 🎨 使用场景

### 场景1: 测试用户登录API
1. 选择预设API: "POST /api/v1/login/google"
2. 在请求体中输入Google ID Token
3. 发送请求获取JWT Token
4. 保存配置以便重复使用

### 场景2: 测试需要认证的API
1. 先通过Google登录获取Token
2. 选择需要认证的API端点
3. 系统自动添加Authorization Header
4. 发送请求查看结果

### 场景3: 自定义API测试
1. 手动输入完整的API URL
2. 设置所需的HTTP方法
3. 添加自定义Headers和参数
4. 构建请求体（如果需要）
5. 发送请求并分析响应

### 场景4: 调试API问题
1. 从历史记录中找到失败的请求
2. 点击加载到当前编辑器
3. 修改参数重新测试
4. 对比不同参数的响应结果

## 🔧 高级功能

### JSON格式化
- 在JSON请求体中输入JSON后点击"格式化JSON"
- 自动检查JSON语法错误
- 美化JSON格式便于阅读

### 批量测试
- 保存多个不同的API配置
- 快速切换配置进行测试
- 通过历史记录对比结果

### 环境切换
```bash
# 切换到开发环境（使用远程API）
python3 switch-env.py development

# 切换回本地环境
python3 switch-env.py local
```

## 🆚 与Postman的对比

| 功能 | Mokta手动测试工具 | Postman |
|------|------------------|---------|
| 🔐 OAuth集成 | ✅ 内置Google OAuth | ⚠️ 需要配置 |
| 📋 预设API | ✅ 基于项目API文档 | ❌ 需要手动创建 |
| 💾 配置保存 | ✅ 本地浏览器存储 | ✅ 云端同步 |
| 📜 请求历史 | ✅ 自动记录 | ✅ 自动记录 |
| 🌐 环境管理 | ✅ 自动检测 | ✅ 手动配置 |
| 📱 轻量级 | ✅ 浏览器运行 | ❌ 需要安装 |
| 🎯 项目专用 | ✅ 专为Mokta设计 | ❌ 通用工具 |

## 🐛 故障排除

### 1. Google登录失败
**问题**: 点击Google登录没有反应或报错
**解决方案**:
- 检查网络连接
- 确保在支持的环境中运行（开发服务器）
- 检查浏览器是否阻止弹窗

### 2. API请求失败
**问题**: 发送请求后返回错误
**解决方案**:
- 检查URL是否正确
- 确认API服务器是否运行
- 检查认证Token是否有效
- 查看响应错误信息

### 3. 配置无法保存
**问题**: 保存的配置丢失
**解决方案**:
- 检查浏览器是否允许本地存储
- 不要使用隐私/无痕模式
- 清除浏览器缓存后重试

### 4. 预设API加载失败
**问题**: 选择预设API后没有自动填充
**解决方案**:
- 检查API配置文件是否正确加载
- 刷新页面重试
- 查看浏览器控制台错误信息

## 💡 最佳实践

1. **先登录**: 建议先进行Google登录获取Token
2. **使用预设**: 优先使用预设API端点，减少手动输入错误
3. **保存配置**: 将常用的API配置保存起来
4. **查看历史**: 利用请求历史对比不同参数的效果
5. **环境切换**: 根据需要在本地和远程环境间切换
6. **导出数据**: 定期导出请求历史作为测试记录

## 🔗 相关链接

- **API文档**: `docs/api.md`
- **自动测试工具**: `python3 start-server.py auto`
- **环境切换**: `python3 switch-env.py`
- **项目README**: `README.md`

---

**提示**: 这个工具专为Mokta项目设计，集成了项目特定的API端点和认证方式，提供比通用工具更好的开发体验。
