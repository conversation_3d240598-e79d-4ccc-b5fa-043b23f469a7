import uuid
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.db import models
from django.utils import timezone


class UserManager(BaseUserManager):
    def get_queryset(self):
        """默认查询集过滤掉已删除的用户"""
        return super().get_queryset().filter(is_deleted=False)

    def all_with_deleted(self):
        """获取包含已删除用户的查询集"""
        return super().get_queryset()

    def deleted_only(self):
        """只获取已删除的用户"""
        return super().get_queryset().filter(is_deleted=True)

    def create_user(self, email, provider, provider_user_id, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(
            email=email,
            provider=provider,
            provider_user_id=provider_user_id,
            **extra_fields
        )
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """创建超级用户 - 自动设置为 admin provider"""
        extra_fields.setdefault('provider', 'admin')
        extra_fields.setdefault('provider_user_id', str(uuid.uuid4()))

        if extra_fields.get('provider') != 'admin':
            raise ValueError('Superuser must have provider="admin".')

        user = self.model(
            email=self.normalize_email(email),
            **extra_fields
        )
        user.set_password(password)
        user.save(using=self._db)
        return user


class User(AbstractBaseUser, PermissionsMixin):
    PROVIDER_CHOICES = [
        ('google', 'Google'),
        ('apple', 'Apple'),
        ('admin', 'Admin'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES)
    provider_user_id = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    nickname = models.CharField(max_length=100, blank=True)
    avatar_url = models.URLField(blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    is_active = models.BooleanField(default=True)

    # 软删除相关字段
    is_deleted = models.BooleanField(
        default=False,
        help_text="用户是否已删除账号（软删除）"
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="账号删除时间"
    )

    # 每日限额配置
    daily_generation_limit = models.PositiveIntegerField(
        default=5,
        help_text="每日免费生成3D角色的次数限制"
    )
    
    objects = UserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    class Meta:
        unique_together = ['provider', 'provider_user_id']
        db_table = 'users'
    
    @property
    def is_staff(self):
        """管理员才能访问 Django admin"""
        return self.provider == 'admin'
    
    @property
    def is_superuser(self):
        """管理员拥有所有权限"""
        return self.provider == 'admin'
    
    def get_today_generation_count(self):
        """获取今日已生成的角色数量"""
        from django.utils import timezone
        today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        return self.generation_tasks.filter(created_at__gte=today_start).count()
    
    def get_remaining_generations_today(self):
        """获取今日剩余生成次数"""
        used = self.get_today_generation_count()
        return max(0, self.daily_generation_limit - used)
    
    def can_generate_today(self):
        """检查今日是否还能生成角色"""
        # 管理员无限制
        if self.is_superuser:
            return True
        return self.get_remaining_generations_today() > 0

    def soft_delete(self):
        """软删除用户账号"""
        self.is_deleted = True
        self.is_active = False  # 同时设置为非活跃状态
        self.deleted_at = timezone.now()
        self.save()

    def restore(self):
        """恢复已删除的用户账号（管理员功能）"""
        self.is_deleted = False
        self.is_active = True
        self.deleted_at = None
        self.save()

    def __str__(self):
        status = " (已删除)" if self.is_deleted else ""
        return f"{self.email} ({self.provider}){status}"
