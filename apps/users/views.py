from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from .serializers import UserSerializer, UserUpdateSerializer, UserUpdateResponseSerializer, UserDeleteSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_today_usage(request):
    """
    获取用户今日使用情况
    """
    user = request.user
    today = timezone.now().date()

    today_count = user.get_today_generation_count()
    remaining = user.get_remaining_generations_today()

    return Response({
        'date': today.isoformat(),
        'total_generations': today_count,
        'daily_limit': user.daily_generation_limit,
        'remaining': remaining,
        'can_generate': user.can_generate_today(),
        'is_admin': user.is_superuser
    })


@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    用户信息管理
    GET /api/v1/users/me - 获取当前用户信息
    PUT /api/v1/users/me - 更新当前用户信息
    """
    user = request.user

    if request.method == 'GET':
        serializer = UserSerializer(user)
        return Response(serializer.data)

    elif request.method == 'PUT':
        serializer = UserUpdateSerializer(user, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            # 返回更新后的用户信息（不包含created_at）
            response_serializer = UserUpdateResponseSerializer(user)
            return Response(response_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_user_account(request):
    """
    删除用户账号（软删除）
    DELETE /api/v1/users/me
    """
    user = request.user

    # 验证删除确认
    serializer = UserDeleteSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # 执行软删除
    user.soft_delete()

    return Response({
        'message': 'Account deleted successfully',
        'deleted_at': user.deleted_at.isoformat()
    }, status=status.HTTP_200_OK)
