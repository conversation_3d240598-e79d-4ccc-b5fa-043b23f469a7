from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin): 
    """用户管理界面配置"""
    
    # 列表页显示字段
    list_display = (
        'id',
        'email', 
        'nickname', 
        'provider', 
        'is_active',
        'daily_generation_limit',
        'admin_status',
        'created_at',
        'is_deleted',
        'provider',
        'created_at',
    )
    
    # 列表页过滤器
    list_filter = (
        'provider', 
        'is_active', 
        'created_at',
        'updated_at',
        'email',
        'nickname',
        'provider',
        'is_deleted',
        'deleted_at',
    )
    
    # 搜索字段
    search_fields = ('email', 'nickname', 'provider_user_id')
    
    # 排序
    ordering = ('-created_at',)
    
    # 每页显示数量
    list_per_page = 20
    
    # 可编辑字段（在列表页直接编辑）
    list_editable = ('is_active', 'daily_generation_limit')
    
    # 只读字段
    readonly_fields = ('id', 'created_at', 'updated_at', 'last_login')
    
    # 字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('email', 'password')
        }),
        ('个人信息', {
            'fields': ('nickname', 'avatar_url')
        }),
        ('认证信息', {
            'fields': ('provider', 'provider_user_id')
        }),
        ('权限设置', {
            'fields': ('is_active', 'daily_generation_limit', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('last_login', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    # 添加用户时的字段
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'provider', 'provider_user_id', 'nickname'),
        }),
    )
    
    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related()
    
    def save_model(self, request, obj, form, change):
        """保存模型时的自定义逻辑"""
        if not change:  # 新建用户
            if not obj.provider_user_id:
                import uuid
                obj.provider_user_id = str(uuid.uuid4())
        super().save_model(request, obj, form, change)
    
    # 自定义操作
    actions = ['activate_users', 'deactivate_users', 'make_admin', 'remove_admin', 'restore_users']
    
    def admin_status(self, obj):
        """显示管理员状态"""
        if obj.is_superuser:
            return format_html('<span style="color: green;">管理员</span>')
        return format_html('<span style="color: gray;">普通用户</span>')
    admin_status.short_description = '管理员状态'
    
    def activate_users(self, request, queryset):
        """批量激活用户"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活了 {updated} 个用户。')
    activate_users.short_description = "激活选中的用户"
    
    def deactivate_users(self, request, queryset):
        """批量停用用户"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用了 {updated} 个用户。')
    deactivate_users.short_description = "停用选中的用户"
    
    def make_admin(self, request, queryset):
        """批量设置为管理员"""
        updated = queryset.update(provider='admin')
        self.message_user(request, f'成功将 {updated} 个用户设置为管理员。')
    make_admin.short_description = "设置为管理员"
    
    def remove_admin(self, request, queryset):
        """批量移除管理员权限"""
        # 注意：这里需要根据原来的认证方式更新provider
        updated = 0
        for user in queryset.filter(provider='admin'):
            # 如果是admin创建的用户，可以考虑设置为其他默认provider
            # 这里简单设置为google，实际应用中可能需要更复杂的逻辑
            user.provider = 'google'
            user.save()
            updated += 1
        self.message_user(request, f'成功移除了 {updated} 个用户的管理员权限。')
    remove_admin.short_description = "移除管理员权限"

    def restore_users(self, request, queryset):
        for user in queryset.filter(is_deleted=True):
            user.restore()
        self.message_user(request, f'成功恢复了 {queryset.count()} 个用户。')
    restore_users.short_description = "恢复选中的用户"
