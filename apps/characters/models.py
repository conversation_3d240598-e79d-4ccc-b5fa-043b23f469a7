import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone


User = get_user_model()


class CharacterManager(models.Manager):
    def get_queryset(self):
        """默认查询集过滤掉已删除的角色"""
        return super().get_queryset().filter(is_deleted=False)

    def all_with_deleted(self):
        """获取包含已删除角色的查询集"""
        return super().get_queryset()

    def deleted_only(self):
        """只获取已删除的角色"""
        return super().get_queryset().filter(is_deleted=True)


class Character(models.Model):
    """
    角色库表 - 存储所有成功生成的3D虚拟角色
    """
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="角色唯一标识符"
    )
    
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='characters',
        help_text="该角色的拥有者"
    )
    
    name = models.CharField(
        max_length=100,
        help_text="角色名称 (可由用户修改)"
    )
    
    model_url = models.TextField(
        help_text="角色 3D 模型文件在对象存储中的 URL"
    )
    
    thumbnail_url = models.TextField(
        blank=True,
        null=True,
        help_text="角色缩略图文件在对象存储中的 URL"
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="存储其他元数据，如动画列表、特效配置等"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="创建时间"
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="更新时间"
    )

    # 软删除相关字段
    is_deleted = models.BooleanField(
        default=False,
        help_text="角色是否已删除（软删除）"
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="角色删除时间"
    )

    objects = CharacterManager()

    class Meta:
        db_table = 'characters'
        indexes = [
            models.Index(fields=['owner'], name='idx_character_owner'),
            models.Index(fields=['created_at'], name='idx_character_created'),
            models.Index(fields=['is_deleted'], name='idx_character_is_deleted'),
            models.Index(fields=['owner', 'is_deleted'], name='idx_character_owner_deleted'),
        ]
        ordering = ['-created_at']
    
    def soft_delete(self):
        """软删除角色"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def restore(self):
        """恢复已删除的角色（管理员功能）"""
        self.is_deleted = False
        self.deleted_at = None
        self.save()

    def __str__(self):
        status = " (已删除)" if self.is_deleted else ""
        return f"{self.name} - {self.owner.email}{status}"
