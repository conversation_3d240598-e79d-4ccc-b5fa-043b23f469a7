# Generated by Django 5.2 on 2025-07-09 15:40

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('characters', '0003_add_updated_at_field'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='character',
            name='deleted_at',
            field=models.DateTimeField(blank=True, help_text='角色删除时间', null=True),
        ),
        migrations.AddField(
            model_name='character',
            name='is_deleted',
            field=models.BooleanField(default=False, help_text='角色是否已删除（软删除）'),
        ),
        migrations.AddIndex(
            model_name='character',
            index=models.Index(fields=['is_deleted'], name='idx_character_is_deleted'),
        ),
        migrations.AddIndex(
            model_name='character',
            index=models.Index(fields=['owner', 'is_deleted'], name='idx_character_owner_deleted'),
        ),
    ]
