from django.contrib import admin
from .models import Character


@admin.register(Character)
class CharacterAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'owner', 'is_deleted', 'deleted_at', 'created_at']
    list_filter = ['is_deleted', 'created_at', 'deleted_at', 'owner']
    search_fields = ['name', 'owner__email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    raw_id_fields = ['owner']
    actions = ['soft_delete_characters', 'restore_characters']
    
    fieldsets = [
        (None, {
            'fields': ['id', 'name', 'owner', 'model_url', 'thumbnail_url']
        }),
        ('元数据', {
            'fields': ['metadata'],
            'classes': ['collapse']
        }),
        ('删除状态', {
            'fields': ['is_deleted', 'deleted_at'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        })
    ]

    def get_queryset(self, request):
        """管理员可以查看包含已删除的所有角色"""
        return Character.objects.all_with_deleted()

    def soft_delete_characters(self, request, queryset):
        """批量软删除角色"""
        count = 0
        for character in queryset.filter(is_deleted=False):
            character.soft_delete()
            count += 1
        self.message_user(request, f'成功软删除了 {count} 个角色。')
    soft_delete_characters.short_description = "软删除选中的角色"

    def restore_characters(self, request, queryset):
        """批量恢复已删除的角色"""
        count = 0
        for character in queryset.filter(is_deleted=True):
            character.restore()
            count += 1
        self.message_user(request, f'成功恢复了 {count} 个角色。')
    restore_characters.short_description = "恢复选中的角色"
