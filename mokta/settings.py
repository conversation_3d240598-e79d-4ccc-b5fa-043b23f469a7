"""
Django settings for mokta project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from urllib.parse import urlparse
from kombu import Queue

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-c3$)_7t%^&vyzog86-^xbezi9h#_ity-ne!wb8t_voa-0ubmdb')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'True').lower() in ('true', '1', 'yes', 'on')

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'apps.authentication',
    'apps.users',
    'apps.characters',
    'apps.tasks',
    'apps.uploads',
    'apps.motions',
]

# 开发环境下添加debug应用
import os
if os.getenv('DEBUG', 'False').lower() == 'true':
    INSTALLED_APPS.append('debug')

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'mokta.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'mokta.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# PostgreSQL Database Configuration
# Parse DATABASE_URL format: postgresql://user:pass@host:port/dbname
db_url = urlparse(os.getenv('DATABASE_URL'))
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': db_url.path[1:],
        'USER': db_url.username,
        'PASSWORD': db_url.password,
        'HOST': db_url.hostname,
        'PORT': db_url.port,
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django Rest Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'apps.authentication.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    origin.strip() for origin in os.getenv(
        'CORS_ALLOWED_ORIGINS', 
        'http://localhost:3000,http://127.0.0.1:3000'
    ).split(',')
]

CORS_ALLOW_CREDENTIALS = True

# CSRF settings
CSRF_TRUSTED_ORIGINS = [
    origin.strip() for origin in os.getenv(
        'CSRF_TRUSTED_ORIGINS', 
        'http://localhost:3000,http://127.0.0.1:3000,https://localhost:3000'
    ).split(',')
]

# CSRF Cookie settings
CSRF_COOKIE_SECURE = os.getenv('CSRF_COOKIE_SECURE', 'False').lower() == 'true'
CSRF_COOKIE_HTTPONLY = os.getenv('CSRF_COOKIE_HTTPONLY', 'False').lower() == 'true'
CSRF_COOKIE_SAMESITE = os.getenv('CSRF_COOKIE_SAMESITE', 'Lax')
CSRF_COOKIE_AGE = int(os.getenv('CSRF_COOKIE_AGE', 31449600))  # 1 year in seconds

# CSRF Header settings
CSRF_HEADER_NAME = os.getenv('CSRF_HEADER_NAME', 'HTTP_X_CSRFTOKEN')
CSRF_USE_SESSIONS = os.getenv('CSRF_USE_SESSIONS', 'False').lower() == 'true'


# Custom User Model
AUTH_USER_MODEL = 'users.User'

# JWT Settings
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', SECRET_KEY)
JWT_EXPIRATION_DAYS = int(os.getenv('JWT_EXPIRATION_DAYS', 7))

# OAuth Settings
GOOGLE_OAUTH_CLIENT_ID = os.getenv('GOOGLE_OAUTH_CLIENT_ID')
APPLE_OAUTH_CLIENT_ID = os.getenv('APPLE_OAUTH_CLIENT_ID', 'com.jz.mokta')  # 默认值匹配JWT中的aud

# Character Access Control Settings
# 控制角色详情API的访问权限
# True: 用户只能访问自己的角色 (默认，有权限隔离)
# False: 用户可以访问所有人的角色
CHARACTER_ACCESS_RESTRICTED = os.getenv('CHARACTER_ACCESS_RESTRICTED', 'True').lower() in ('true', '1', 'yes', 'on')

# Celery Configuration
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6380/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6380/1')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Celery 异常处理配置
CELERY_TASK_REMOTE_TRACEBACKS = True
CELERY_TASK_ALWAYS_EAGER = False
CELERY_RESULT_EXPIRES = 3600  # 1小时
CELERY_TASK_TIME_LIMIT = 1800  # 30分钟
CELERY_TASK_SOFT_TIME_LIMIT = 1500  # 25分钟软限制

# Celery 异常序列化配置
CELERY_TASK_IGNORE_RESULT = False
CELERY_TASK_STORE_EAGER_RESULT = True
CELERY_TASK_STORE_FAILURES_EVEN_IF_IGNORED = True

# Celery 结果后端配置
CELERY_RESULT_BACKEND_TRANSPORT_OPTIONS = {
    'retry_policy': {
        'timeout': 5.0
    }
}

# Object Storage Settings (MinIO/S3)
# MinIO Configuration
MINIO_ENDPOINT = os.getenv('MINIO_ENDPOINT', '')
MINIO_ACCESS_KEY = os.getenv('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.getenv('MINIO_SECRET_KEY', '')
MINIO_BUCKET = os.getenv('MINIO_BUCKET', 'test1')
MINIO_SECURE = os.getenv('MINIO_SECURE', 'false').lower() == 'true'

# AWS S3 Settings (fallback or alternative)
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', MINIO_ACCESS_KEY)
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', MINIO_SECRET_KEY)
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', MINIO_BUCKET)
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-east-1')

# Storage Configuration - Use MinIO if endpoint is provided, otherwise S3
USE_MINIO = bool(MINIO_ENDPOINT)
STORAGE_ENDPOINT = MINIO_ENDPOINT if USE_MINIO else None
STORAGE_ACCESS_KEY = MINIO_ACCESS_KEY if USE_MINIO else AWS_ACCESS_KEY_ID
STORAGE_SECRET_KEY = MINIO_SECRET_KEY if USE_MINIO else AWS_SECRET_ACCESS_KEY
STORAGE_BUCKET_NAME = MINIO_BUCKET if USE_MINIO else AWS_STORAGE_BUCKET_NAME
STORAGE_SECURE = MINIO_SECURE if USE_MINIO else True

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}
