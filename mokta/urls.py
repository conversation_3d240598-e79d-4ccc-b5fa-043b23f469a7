"""
URL configuration for mokta project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/v1/', include('apps.authentication.urls')),
    path('api/v1/upload/', include('apps.uploads.urls')),
    path('api/v1/characters/', include('apps.characters.urls')),
    path('api/v1/tasks/', include('apps.tasks.urls')),
    path('api/v1/users/', include('apps.users.urls')),
    path('api/v1/motions/', include('apps.motions.urls')),
]

# 开发环境下启用debug工具
if settings.DEBUG:
    urlpatterns += [
        path('debug/', include('debug.urls')),
    ]
