// Mokta Frontend Test - Main Application Script

// 配置
const API_BASE = 'https://pqqjrlgoetaa.usw.sealos.io';
let currentUser = null;
let jwtToken = null;
let selectedFile = null;
let currentTaskId = null;
let pollingInterval = null;
let currentModelUrl = null;
let currentThumbnailUrl = null;
let charactersData = [];
let motionsData = [];

// Google 登录回调
function handleCredentialResponse(response) {
    console.log("开始 Google 登录验证...");
    
    fetch(`${API_BASE}/api/v1/login/google`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id_token: response.credential
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.access_token) {
            // 登录成功
            jwtToken = data.access_token;
            currentUser = data.user;
            
            showLoginSuccess(data);
            showMainArea();
        } else {
            showError('登录失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('登录失败:', error);
        showError('登录失败: ' + error.message);
    });
}

// 显示登录成功
function showLoginSuccess(data) {
    document.getElementById('loginStatus').className = 'status success';
    document.getElementById('loginStatus').textContent = '✅ 登录成功';
    
    document.getElementById('userEmail').textContent = data.user.email;
    document.getElementById('userNickname').textContent = data.user.nickname || '未设置';
    
    // 显示配额容器
    document.getElementById('quotaContainer').style.display = 'block';
    
    // 加载配额信息
    loadQuotaInfo();
    
    // 加载角色列表和动作列表
    loadCharactersList();
    loadMotionsList();
    
    document.getElementById('googleLogin').style.display = 'none';
    document.getElementById('userInfo').style.display = 'block';
}

// 显示主要功能区域
function showMainArea() {
    document.getElementById('mainArea').classList.remove('hidden');
}

// 加载配额信息
async function loadQuotaInfo() {
    try {
        const response = await fetch(`${API_BASE}/api/v1/users/me/usage/today`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const quotaData = await response.json();
            updateQuotaDisplay(quotaData);
        } else {
            console.error('加载配额信息失败:', response.status);
            showQuotaError();
        }
    } catch (error) {
        console.error('加载配额信息失败:', error);
        showQuotaError();
    }
}

// 更新配额显示
function updateQuotaDisplay(quotaData) {
    const { total_generations, daily_limit, remaining, can_generate, is_admin } = quotaData;
    
    // 更新数字显示
    document.getElementById('quotaUsed').textContent = total_generations;
    document.getElementById('quotaLimit').textContent = daily_limit;
    document.getElementById('quotaRemaining').textContent = remaining;
    
    // 计算进度百分比
    const percentage = daily_limit > 0 ? (total_generations / daily_limit) * 100 : 0;
    document.getElementById('quotaFill').style.width = percentage + '%';
    
    // 更新状态显示
    const statusElement = document.getElementById('quotaStatus');
    
    if (is_admin) {
        statusElement.textContent = '👑 管理员账户 - 无限制';
        statusElement.className = 'quota-status available';
    } else if (!can_generate) {
        statusElement.textContent = '❌ 今日配额已用完，请明天再试';
        statusElement.className = 'quota-status exhausted';
    } else if (remaining <= 1) {
        statusElement.textContent = '⚠️ 配额即将用完';
        statusElement.className = 'quota-status warning';
    } else {
        statusElement.textContent = '✅ 配额充足';
        statusElement.className = 'quota-status available';
    }
    
    // 根据配额状态控制生成按钮
    updateGenerateButtonState(can_generate, selectedFile);
}

// 更新生成按钮状态
function updateGenerateButtonState(canGenerate, hasFile) {
    const generateBtn = document.getElementById('generateBtn');
    
    if (!hasFile) {
        generateBtn.disabled = true;
        generateBtn.textContent = '🚀 上传并生成角色';
    } else if (!canGenerate) {
        generateBtn.disabled = true;
        generateBtn.textContent = '😢 配额已用完';
        generateBtn.style.opacity = '0.6';
    } else {
        generateBtn.disabled = false;
        generateBtn.textContent = '🚀 上传并生成角色';
        generateBtn.style.opacity = '1';
    }
}

// 显示配额错误
function showQuotaError() {
    document.getElementById('quotaUsed').textContent = '?';
    document.getElementById('quotaLimit').textContent = '?';
    document.getElementById('quotaRemaining').textContent = '?';
    document.getElementById('quotaFill').style.width = '0%';
    
    const statusElement = document.getElementById('quotaStatus');
    statusElement.textContent = '❌ 无法加载配额信息';
    statusElement.className = 'quota-status exhausted';
}

// 退出登录
function logout() {
    jwtToken = null;
    currentUser = null;
    selectedFile = null;
    
    // 停止轮询
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    
    // 重置UI
    document.getElementById('loginStatus').className = 'status info';
    document.getElementById('loginStatus').textContent = '未登录 - 请先进行 Google 登录';
    
    document.getElementById('googleLogin').style.display = 'block';
    document.getElementById('userInfo').style.display = 'none';
    document.getElementById('quotaContainer').style.display = 'none';
    document.getElementById('mainArea').classList.add('hidden');
    
    // 重置文件选择
    resetFileSelection();
    
    // 清空角色列表和动作列表
    charactersData = [];
    motionsData = [];
    updateCharactersStatus('', 'info');
    updateMotionsStatus('', 'info');
    showEmptyCharactersState();
    showEmptyMotionsState();
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processSelectedFile(file);
    }
}

// 处理文件拖拽
function setupDragAndDrop() {
    const uploadArea = document.getElementById('uploadArea');
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (isValidFileType(file)) {
                processSelectedFile(file);
            } else {
                showError('请选择 JPG 或 PNG 格式的图片文件');
            }
        }
    });
}

// 验证文件类型
function isValidFileType(file) {
    const validTypes = ['image/jpeg', 'image/png'];
    return validTypes.includes(file.type);
}

// 处理选中的文件
function processSelectedFile(file) {
    if (!isValidFileType(file)) {
        showError('请选择 JPG 或 PNG 格式的图片文件');
        return;
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB 限制
        showError('文件大小不能超过 10MB');
        return;
    }
    
    selectedFile = file;
    
    // 显示文件信息
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileType').textContent = file.type;
    document.getElementById('fileInfo').classList.remove('hidden');
    
    // 显示预览图片
    const reader = new FileReader();
    reader.onload = (e) => {
        const previewImage = document.getElementById('previewImage');
        previewImage.src = e.target.result;
        previewImage.classList.remove('hidden');
        document.getElementById('uploadPrompt').style.display = 'none';
    };
    reader.readAsDataURL(file);
    
    // 重新加载配额信息以更新按钮状态
    loadQuotaInfo();
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}

// 重置文件选择
function resetFileSelection() {
    selectedFile = null;
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').classList.add('hidden');
    document.getElementById('previewImage').classList.add('hidden');
    document.getElementById('uploadPrompt').style.display = 'block';
    document.getElementById('generateBtn').disabled = true;
    document.getElementById('progressBar').style.display = 'none';
    document.getElementById('statusMessage').classList.add('hidden');
    document.getElementById('resultSection').style.display = 'none';
    resetDownloadArea();
}

// 开始上传和生成流程
async function startUploadAndGenerate() {
    if (!selectedFile) {
        showError('请先选择图片文件');
        return;
    }
    
    try {
        // 禁用按钮
        document.getElementById('generateBtn').disabled = true;
        document.getElementById('progressBar').style.display = 'block';
        
        // 步骤1: 获取预签名上传URL
        updateStatus('📤 正在获取上传地址...', 'info');
        updateProgress(10);
        
        const uploadData = await getPresignedUrl();
        console.log('获取到上传URL:', uploadData);
        
        // 步骤2: 上传文件到 MinIO
        updateStatus('⬆️ 正在上传图片...', 'info');
        updateProgress(30);
        
        await uploadFileToMinio(uploadData.upload_url, selectedFile);
        console.log('文件上传完成');
        
        // 步骤3: 创建角色生成任务
        updateStatus('🎭 正在创建生成任务...', 'info');
        updateProgress(50);
        
        const taskData = await createGenerationTask(uploadData.file_key);
        console.log('生成任务已创建:', taskData);
        
        currentTaskId = taskData.task_id;
        updateProgress(60);
        
        // 步骤4: 开始监控任务状态
        updateStatus('⏳ 正在生成角色，请稍候...', 'warning');
        updateProgress(70);
        
        showResultSection();
        startPolling();
        
    } catch (error) {
        console.error('流程错误:', error);
        
        // 配额超出的情况下不显示通用错误，因为已经有专门的处理
        if (error.message !== 'quota_exceeded') {
            updateStatus('❌ ' + error.message, 'error');
        }
        
        document.getElementById('generateBtn').disabled = false;
    }
}

// 获取预签名上传URL
async function getPresignedUrl() {
    const response = await fetch(`${API_BASE}/api/v1/upload/presigned-url`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
            file_name: selectedFile.name,
            file_type: selectedFile.type,
            file_size: selectedFile.size
        })
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '获取上传地址失败');
    }
    
    return await response.json();
}

// 上传文件到 MinIO
async function uploadFileToMinio(uploadUrl, file) {
    console.log('使用上传URL:', uploadUrl);
    
    const response = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
            'Content-Type': file.type
        }
    });
    
    if (!response.ok) {
        console.error('上传失败，状态码:', response.status);
        console.error('响应文本:', await response.text());
        throw new Error(`文件上传失败: ${response.status} ${response.statusText}`);
    }
}

// 创建角色生成任务
async function createGenerationTask(fileKey) {
    const quality = document.getElementById('qualitySelect').value;
    
    const response = await fetch(`${API_BASE}/api/v1/characters/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
            source_photo_key: fileKey,
            quality: quality
        })
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        
        // 处理超出限额的特殊情况
        if (response.status === 429 && errorData.error === 'daily_limit_exceeded') {
            handleQuotaExceeded(errorData);
            throw new Error('quota_exceeded'); // 特殊错误标识
        }
        
        throw new Error(errorData.message || errorData.error || '创建生成任务失败');
    }
    
    return await response.json();
}

// 处理配额超出情况
function handleQuotaExceeded(errorData) {
    const { message, today_usage } = errorData;
    
    // 更新配额显示
    if (today_usage) {
        updateQuotaDisplay({
            total_generations: today_usage.total_generations,
            daily_limit: today_usage.daily_limit,
            remaining: today_usage.remaining,
            can_generate: false,
            is_admin: false
        });
    }
    
    // 显示友好的错误提示
    showError(`😢 ${message}<br><br>💡 提示：配额将在明天重置，或联系管理员提升限额`);
}

// 开始轮询任务状态
function startPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
    
    document.getElementById('stopPollingBtn').classList.remove('hidden');
    
    // 立即查询一次
    checkTaskStatus();
    
    // 每3秒查询一次
    pollingInterval = setInterval(checkTaskStatus, 3000);
}

// 检查任务状态
async function checkTaskStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`${API_BASE}/api/v1/tasks/${currentTaskId}/status`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`
            }
        });
        
        if (!response.ok) {
            throw new Error('查询任务状态失败');
        }
        
        const statusData = await response.json();
        const timestamp = new Date().toLocaleTimeString();
        
        document.getElementById('taskInfo').textContent = 
            `[${timestamp}] ${JSON.stringify(statusData, null, 2)}`;
        
        // 根据状态更新进度
        if (statusData.status === 'SUCCESS') {
            updateStatus('✅ 角色生成完成！', 'success');
            updateProgress(100);
            stopPolling();
            document.getElementById('generateBtn').disabled = false;
            
            // 显示下载按钮和缩略图
            showDownloadButton(statusData.result);
            
            // 更新配额信息（成功完成会消耗配额）
            loadQuotaInfo();
            
            // 刷新角色列表（新生成的角色会显示）
            loadCharactersList();
            loadMotionsList();
            
        } else if (statusData.status === 'FAILURE') {
            updateStatus('❌ 角色生成失败: ' + (statusData.error || '未知错误'), 'error');
            stopPolling();
            document.getElementById('generateBtn').disabled = false;
            
            // 失败时也更新配额信息（失败也会消耗配额）
            loadQuotaInfo();
        } else if (statusData.status === 'PENDING') {
            updateStatus('⏳ 任务排队中...', 'warning');
            updateProgress(75);
        } else if (statusData.status === 'STARTED') {
            updateStatus('🎨 正在生成角色...', 'warning');
            updateProgress(85);
        }
        
    } catch (error) {
        console.error('查询任务状态失败:', error);
    }
}

// 停止轮询
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    document.getElementById('stopPollingBtn').classList.add('hidden');
}

// 显示结果区域
function showResultSection() {
    document.getElementById('resultSection').style.display = 'block';
}

// 更新状态消息
function updateStatus(message, type = 'info') {
    const statusElement = document.getElementById('statusMessage');
    statusElement.innerHTML = message; // 使用innerHTML支持HTML内容
    statusElement.className = `status ${type}`;
    statusElement.classList.remove('hidden');
}

// 更新进度条
function updateProgress(percentage) {
    document.getElementById('progressFill').style.width = percentage + '%';
}

// 显示下载按钮和缩略图
function showDownloadButton(result) {
    if (result && result.model_url) {
        currentModelUrl = result.model_url;
        currentThumbnailUrl = result.thumbnail_url;
        document.getElementById('downloadArea').classList.remove('hidden');
        
        // 显示缩略图
        if (result.thumbnail_url) {
            showThumbnail(result.thumbnail_url);
        }
        
        console.log('模型下载地址:', result.model_url);
        console.log('缩略图地址:', result.thumbnail_url);
    }
}

// 下载3D模型
function downloadModel() {
    if (!currentModelUrl) {
        showError('没有可下载的模型文件');
        return;
    }

    try {
        // 确保URL有协议前缀
        let downloadUrl = currentModelUrl;
        if (!downloadUrl.startsWith('http://') && !downloadUrl.startsWith('https://')) {
            downloadUrl = 'https://' + downloadUrl;
        }
        
        // 从URL中提取文件名
        const url = new URL(downloadUrl);
        const pathParts = url.pathname.split('/');
        const fileName = pathParts[pathParts.length - 1] || 'character_model.glb';
        
        console.log('开始下载模型:', downloadUrl);
        
        // 创建一个隐藏的下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        // 添加到页面并触发下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        document.body.removeChild(downloadLink);
        
        // 显示下载提示
        updateStatus('📥 开始下载3D模型文件...', 'info');
        
    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

// 重置下载区域
function resetDownloadArea() {
    currentModelUrl = null;
    currentThumbnailUrl = null;
    document.getElementById('downloadArea').classList.add('hidden');
    
    // 隐藏缩略图
    const thumbnailImg = document.getElementById('thumbnailImage');
    if (thumbnailImg) {
        thumbnailImg.classList.add('hidden');
    }
}

// 显示缩略图
function showThumbnail(thumbnailUrl) {
    let thumbnailImg = document.getElementById('thumbnailImage');
    
    // 如果缩略图元素不存在，创建它
    if (!thumbnailImg) {
        const downloadArea = document.getElementById('downloadArea');
        
        // 创建缩略图容器
        const thumbnailContainer = document.createElement('div');
        thumbnailContainer.style.cssText = 'margin: 20px 0; text-align: center;';
        
        // 创建标题
        const thumbnailTitle = document.createElement('h4');
        thumbnailTitle.textContent = '🖼️ 角色预览';
        thumbnailTitle.style.cssText = 'margin-bottom: 10px; color: #333;';
        
        // 创建图片元素
        thumbnailImg = document.createElement('img');
        thumbnailImg.id = 'thumbnailImage';
        thumbnailImg.style.cssText = `
            max-width: 300px; 
            max-height: 300px; 
            border-radius: 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 2px solid #ddd;
        `;
        thumbnailImg.alt = '角色缩略图';
        
        thumbnailContainer.appendChild(thumbnailTitle);
        thumbnailContainer.appendChild(thumbnailImg);
        
        // 插入到下载按钮前面
        const downloadBtn = document.getElementById('downloadBtn');
        downloadArea.insertBefore(thumbnailContainer, downloadBtn);
    }
    
    // 设置图片地址并显示
    thumbnailImg.src = thumbnailUrl;
    thumbnailImg.classList.remove('hidden');
    thumbnailImg.onerror = function() {
        console.log('缩略图加载失败:', thumbnailUrl);
        this.style.display = 'none';
    };
}

// 显示错误信息
function showError(message) {
    updateStatus('❌ ' + message, 'error');
}

// ===========================================
// 角色列表功能
// ===========================================

// 获取用户角色列表
async function loadCharactersList() {
    if (!jwtToken) {
        console.log('未登录，跳过角色列表加载');
        return;
    }

    try {
        updateCharactersStatus('正在加载角色列表...', 'info');
        
        const response = await fetch(`${API_BASE}/api/v1/characters/?page=1&page_size=20`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        charactersData = data.characters || [];
        
        console.log(`加载了 ${charactersData.length} 个角色`);
        console.log('角色数据:', charactersData);
        
        // 显示角色列表
        displayCharactersList();
        
        if (charactersData.length === 0) {
            updateCharactersStatus('', 'info');
            showEmptyCharactersState();
        } else {
            updateCharactersStatus(`共 ${data.total} 个角色`, 'success');
        }
        
    } catch (error) {
        console.error('加载角色列表失败:', error);
        updateCharactersStatus('加载角色列表失败: ' + error.message, 'error');
    }
}

// 显示角色列表
function displayCharactersList() {
    const grid = document.getElementById('charactersGrid');
    const emptyState = document.getElementById('charactersEmpty');
    
    // 清空现有内容
    grid.innerHTML = '';
    
    if (charactersData.length === 0) {
        emptyState.classList.remove('hidden');
        return;
    }
    
    emptyState.classList.add('hidden');
    
    // 创建角色项目
    charactersData.forEach(character => {
        const characterItem = createCharacterItem(character);
        grid.appendChild(characterItem);
    });
}

// 创建单个角色项目
function createCharacterItem(character) {
    const item = document.createElement('div');
    item.className = 'character-item';
    item.onclick = () => downloadCharacterModel(character);
    
    // 格式化创建日期
    const createdDate = new Date(character.created_at).toLocaleDateString('zh-CN');
    
    // 构建HTML内容
    let thumbnailHtml = '';
    if (character.thumbnail_url) {
        console.log(`角色 ${character.name} 的缩略图URL:`, character.thumbnail_url);
        thumbnailHtml = `<img class="character-thumbnail" src="${character.thumbnail_url}" alt="${character.name}">`;
    } else {
        console.log(`角色 ${character.name} 没有缩略图`);
    }
    
    item.innerHTML = `
        ${thumbnailHtml}
        <p class="character-name">${character.name}</p>
        <p class="character-date">${createdDate}</p>
    `;
    
    return item;
}

// 下载角色模型
function downloadCharacterModel(character) {
    if (!character.model_url) {
        showError('该角色没有可下载的模型文件');
        return;
    }
    
    console.log('开始下载角色模型:', character.name);
    
    try {
        // 确保URL有协议前缀
        let downloadUrl = character.model_url;
        if (!downloadUrl.startsWith('http://') && !downloadUrl.startsWith('https://')) {
            downloadUrl = 'https://' + downloadUrl;
        }
        
        // 从URL中提取文件名，或使用角色名
        const url = new URL(downloadUrl);
        const pathParts = url.pathname.split('/');
        const fileName = pathParts[pathParts.length - 1] || `${character.name}.glb`;
        
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        // 添加到页面并触发下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        document.body.removeChild(downloadLink);
        
        // 显示下载提示
        updateCharactersStatus(`正在下载 ${character.name}...`, 'info');
        
    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

// 显示空状态
function showEmptyCharactersState() {
    const grid = document.getElementById('charactersGrid');
    const emptyState = document.getElementById('charactersEmpty');
    
    grid.innerHTML = '';
    emptyState.classList.remove('hidden');
}

// 更新角色列表状态信息
function updateCharactersStatus(message, type = 'info') {
    const statusElement = document.getElementById('charactersStatus');
    if (!statusElement) return;
    
    statusElement.textContent = message;
    statusElement.className = `status ${type}`;
    
    if (!message) {
        statusElement.style.display = 'none';
    } else {
        statusElement.style.display = 'block';
    }
}

// ==================== 动作库相关功能 ====================

// 加载动作列表
async function loadMotionsList() {
    if (!jwtToken) {
        console.log('未登录，跳过动作列表加载');
        return;
    }

    try {
        updateMotionsStatus('正在加载动作列表...', 'info');
        
        console.log('=== 动作列表API请求 ===');
        const requestUrl = `${API_BASE}/api/v1/motions/?page=1&page_size=20`;
        const requestHeaders = {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        };
        
        console.log('请求URL:', requestUrl);
        console.log('请求头:', requestHeaders);
        
        const response = await fetch(requestUrl, {
            method: 'GET',
            headers: requestHeaders
        });

        console.log('响应状态:', response.status);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API响应数据:', data);
        
        motionsData = data.motions || [];
        
        console.log(`加载了 ${motionsData.length} 个动作`);
        console.log('动作数据:', motionsData);
        
        // 为每个动作打印详细信息
        motionsData.forEach((motion, index) => {
            console.log(`动作 ${index + 1}:`, {
                id: motion.id,
                name: motion.name,
                motion_url: motion.motion_url,
                preview_video_url: motion.preview_video_url,
                creator: motion.creator,
                created_at: motion.created_at
            });
        });
        
        // 显示动作列表
        displayMotionsList();
        
        if (motionsData.length === 0) {
            updateMotionsStatus('', 'info');
            showEmptyMotionsState();
        } else {
            updateMotionsStatus(`共 ${data.total} 个动作`, 'success');
        }
        
    } catch (error) {
        console.error('加载动作列表失败:', error);
        updateMotionsStatus('加载动作列表失败: ' + error.message, 'error');
    }
}

// 显示动作列表
function displayMotionsList() {
    const grid = document.getElementById('motionsGrid');
    const emptyState = document.getElementById('motionsEmpty');
    
    // 清空现有内容
    grid.innerHTML = '';
    
    if (motionsData.length === 0) {
        emptyState.classList.remove('hidden');
        return;
    }
    
    emptyState.classList.add('hidden');
    
    // 创建动作项目
    motionsData.forEach(motion => {
        const motionItem = createMotionItem(motion);
        grid.appendChild(motionItem);
    });
}

// 创建单个动作项目
function createMotionItem(motion) {
    const item = document.createElement('div');
    item.className = 'motion-item';
    item.onclick = () => downloadMotionFile(motion);
    
    // 格式化创建日期
    const createdDate = new Date(motion.created_at).toLocaleDateString('zh-CN');
    
    // 构建预览视频HTML
    let videoHtml = '';
    if (motion.preview_video_url) {
        console.log(`动作 ${motion.name} 的预览视频URL:`, motion.preview_video_url);
        videoHtml = `<video class="motion-video" src="${motion.preview_video_url}" 
                           autoplay muted loop playsinline
                           alt="${motion.name} 预览视频">
                     </video>`;
    } else {
        console.log(`动作 ${motion.name} 没有预览视频`);
        // 如果没有预览视频，显示占位符
        videoHtml = `<div class="motion-video" style="background-color: #e9ecef; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                       🎬<br>无预览
                     </div>`;
    }
    
    item.innerHTML = `
        ${videoHtml}
        <p class="motion-name">${motion.name}</p>
        <p class="motion-creator">创建者: ${motion.creator || '未知'}</p>
        <p class="motion-date">${createdDate}</p>
        <p class="motion-download-hint">点击下载动作文件</p>
    `;
    
    return item;
}

// 下载动作文件
function downloadMotionFile(motion) {
    if (!motion.motion_url) {
        showError('该动作没有可下载的文件');
        return;
    }
    
    console.log('开始下载动作文件:', motion.name);
    
    try {
        // 确保URL有协议前缀
        let downloadUrl = motion.motion_url;
        if (!downloadUrl.startsWith('http://') && !downloadUrl.startsWith('https://')) {
            downloadUrl = 'https://' + downloadUrl;
        }
        
        // 从URL中提取文件名，或使用动作名
        const url = new URL(downloadUrl);
        const pathParts = url.pathname.split('/');
        let fileName = pathParts[pathParts.length - 1];
        
        // 如果无法从URL获取文件名，使用动作名
        if (!fileName || !fileName.includes('.')) {
            fileName = `${motion.name}.fbx`;
        }
        
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';
        
        // 添加到页面并触发下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        document.body.removeChild(downloadLink);
        
        // 显示成功消息
        updateMotionsStatus(`正在下载 ${motion.name}...`, 'success');
        
        console.log('动作文件下载已开始:', fileName);
        
    } catch (error) {
        console.error('下载动作文件失败:', error);
        showError('下载失败: ' + error.message);
    }
}

// 显示空的动作状态
function showEmptyMotionsState() {
    const grid = document.getElementById('motionsGrid');
    const emptyState = document.getElementById('motionsEmpty');
    
    grid.innerHTML = '';
    emptyState.classList.remove('hidden');
}

// 更新动作状态信息
function updateMotionsStatus(message, type = 'info') {
    const statusElement = document.getElementById('motionsStatus');
    if (!statusElement) return;
    
    statusElement.textContent = message;
    statusElement.className = `status ${type}`;
    
    if (!message) {
        statusElement.style.display = 'none';
    } else {
        statusElement.style.display = 'block';
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('测试页面已加载');
    console.log('后端 API 地址:', API_BASE);
    setupDragAndDrop();
});