#!/usr/bin/env python3
"""
启动简单的 HTTP 服务器来运行前端测试页面
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# 服务器配置
PORT = 3000
HOST = 'localhost'

def start_server():
    # 切换到前端测试目录
    frontend_dir = Path(__file__).parent
    os.chdir(frontend_dir)
    
    print(f"🚀 启动前端测试服务器...")
    print(f"📁 工作目录: {frontend_dir}")
    print(f"🌐 服务器地址: http://{HOST}:{PORT}")
    print(f"📄 测试页面: http://{HOST}:{PORT}/index.html")
    print("-" * 50)
    
    # 创建 HTTP 服务器
    handler = http.server.SimpleHTTPRequestHandler
    handler.extensions_map.update({
        '.js': 'application/javascript',
        '.html': 'text/html',
        '.css': 'text/css',
    })
    
    try:
        with socketserver.TCPServer((HOST, PORT), handler) as httpd:
            print(f"✅ 服务器启动成功: http://{HOST}:{PORT}")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}/index.html')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️  无法自动打开浏览器，请手动访问上述地址")
            
            print()
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print(f"💡 尝试直接访问: http://{HOST}:{PORT}/index.html")
            print(f"💡 或者运行: python -m http.server {PORT}")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        sys.exit(0)

if __name__ == '__main__':
    start_server()