# Mokta API 前端测试页面

这是一个简单的静态网页，用于测试 Mokta 后端 API 的所有功能。

## 🚀 快速开始

### 1. 启动后端服务器
```bash
source .venv/bin/activate
python manage.py runserver
```

### 2. 启动前端测试服务器
```bash
# 方法1：使用提供的脚本
cd ./frontend-test
python start-test-server.py

# 方法2：使用 Python 内置服务器
cd ./frontend-test
python -m http.server 3000

# 方法3：直接双击 HTML 文件（不推荐，可能有 CORS 问题）
open index.html
```

### 3. 访问测试页面
浏览器自动打开或手动访问：
```
http://localhost:3000/index.html
```

## 🧪 测试功能

### ✅ 已实现的测试功能

1. **Google OAuth 登录**
   - 点击 Google 登录按钮
   - 获取 JWT token
   - 显示用户信息

2. **文件上传 API 测试**
   - 获取预签名上传 URL
   - 支持自定义文件名、类型、大小

3. **角色生成 API 测试**
   - 创建角色生成任务
   - 返回任务 ID

4. **任务状态查询**
   - 手动查询任务状态
   - 自动轮询任务状态

5. **自定义 API 测试**
   - 支持任意 HTTP 方法
   - 自定义 URL 和请求体
   - 自动添加 JWT 认证头

## 📋 测试流程

### 标准测试流程：
1. **登录** → 点击 Google 登录按钮
2. **文件上传** → 测试获取上传 URL
3. **角色生成** → 创建生成任务
4. **状态查询** → 监控任务进度
5. **自定义测试** → 测试其他 API 端点

### 预期结果：
- ✅ **登录成功**：显示用户信息和 JWT token
- ✅ **上传 URL**：返回预签名 URL 和文件 key
- ⚠️ **角色生成**：创建任务（但可能失败，因为 MinIO/Celery 服务未运行）
- ⚠️ **任务状态**：返回任务状态（可能是 PENDING 或 FAILURE）

## 🔧 配置说明

### Google OAuth 配置
当前使用的 Client ID：
```
930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com
```

### API 端点配置
默认后端地址：
```javascript
const API_BASE = 'https://pqqjrlgoetaa.usw.sealos.io';
```

如需修改，编辑 `index.html` 中的 `API_BASE` 变量。

## 🐛 常见问题

### 1. CORS 错误
确保后端 Django 服务器正在运行，并且 CORS 配置正确：
```bash
# .env 文件中
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### 2. Google 登录失败
- 检查 Google OAuth Client ID 是否正确
- 确保访问地址在 Google Console 的授权域名中
- 查看浏览器控制台的错误信息

### 3. API 请求失败
- 确保 Django 服务器运行在 `https://pqqjrlgoetaa.usw.sealos.io`
- 检查是否已正确登录获取 JWT token
- 查看网络请求的详细错误信息

### 4. 任务状态始终为 PENDING
这是正常的，因为：
- Celery 服务器可能未运行
- MinIO 对象存储可能不可用
- 这不影响 API 接口的测试

## 📊 测试报告

使用此页面可以验证：
- ✅ Django 服务器运行正常
- ✅ Google OAuth 集成工作
- ✅ JWT 认证系统正常
- ✅ API 路由配置正确
- ✅ CORS 配置正确
- ✅ 数据库连接正常

## 🔄 开发调试

### 查看控制台日志
打开浏览器开发者工具 (F12)，查看：
- **Console**: JavaScript 日志和错误
- **Network**: API 请求和响应
- **Application**: 本地存储和 Cookies

### 修改测试参数
直接在页面上修改：
- 文件名、类型、大小
- 角色名称
- API 路径和请求体

## 📝 注意事项

⚠️ **此页面仅用于开发测试**
- 不要在生产环境使用
- JWT token 明文显示，注意安全
- 某些功能依赖外部服务，可能会失败

✅ **测试重点**
- 专注于测试 Django API 的可用性
- 验证认证和授权机制
- 确认 JSON 数据格式正确