# 🐛 Debug 工具集合

这个目录包含了用于调试和测试Mokta后端API的工具集合，部署在开发服务器的子路径下。

## 📁 目录结构

```
debug/
├── api-tester/          # API测试工具
│   ├── index.html       # 自动化API测试
│   ├── manual.html      # 手动API测试（类似Postman）
│   ├── js/             # JavaScript文件
│   ├── css/            # 样式文件
│   └── config/         # 配置文件
├── frontend-test/       # 前端测试页面（现有的）
│   ├── index.html
│   ├── app.js
│   └── style.css
└── views.py            # Django视图（用于服务静态文件）
```

## 🌐 访问地址

### 开发服务器访问
- **API测试工具**: https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/
- **手动API测试**: https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/manual/
- **前端测试页面**: https://pqqjrlgoetaa.usw.sealos.io/debug/frontend-test/

### 本地开发访问
- **API测试工具**: http://localhost:8000/debug/api-tester/
- **手动API测试**: http://localhost:8000/debug/api-tester/manual/
- **前端测试页面**: http://localhost:8000/debug/frontend-test/

## ✅ 优势

1. **🔗 统一域名**: 使用相同域名，避免CORS问题
2. **🔐 OAuth兼容**: 重定向URI匹配，Google OAuth正常工作
3. **🚀 零配置**: 无需额外端口，直接通过Django服务
4. **📱 易访问**: 通过浏览器直接访问，无需启动额外服务
5. **🔄 热更新**: 修改文件后刷新即可看到效果

## 🛠️ 部署说明

### 1. 集成到Django项目
在 `mokta/urls.py` 中添加debug路由：

```python
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # ... 现有路由
    path('debug/', include('debug.urls')),
]

# 开发环境下服务debug静态文件
if settings.DEBUG:
    urlpatterns += static('debug/', document_root='debug')
```

### 2. 更新环境配置
API测试工具会自动检测运行环境：
- 检测到 `pqqjrlgoetaa.usw.sealos.io` 域名时使用开发环境配置
- 自动使用正确的API基础URL和OAuth配置

### 3. 文件同步
```bash
# 将现有的测试工具复制到debug目录
cp -r api-tester debug/
cp -r frontend-test debug/

# 更新配置文件中的路径
# 修改JavaScript中的相对路径引用
```

## 🔧 配置更新

### API基础URL自动检测
```javascript
// 自动检测API基础URL
const getApiBaseUrl = () => {
    const hostname = window.location.hostname;
    if (hostname.includes('sealos.io')) {
        return 'https://pqqjrlgoetaa.usw.sealos.io';
    } else if (hostname === 'localhost') {
        return 'http://localhost:8000';
    }
    return window.location.origin;
};
```

### OAuth重定向URI
```javascript
// 自动配置OAuth重定向URI
const getOAuthRedirectUri = () => {
    return window.location.origin + '/debug/api-tester/';
};
```

## 📋 使用流程

### 开发服务器使用
1. 访问 https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/
2. 点击Google登录（重定向URI自动匹配）
3. 获取JWT Token后开始API测试
4. 所有API请求自动发送到正确的后端地址

### 本地开发使用
1. 启动Django开发服务器：`python manage.py runserver`
2. 访问 http://localhost:8000/debug/api-tester/
3. 使用手动Token输入或切换到开发环境进行OAuth登录

## 🔄 文件更新策略

### 自动同步（推荐）
创建软链接，保持文件同步：
```bash
# 创建软链接
ln -s ../api-tester debug/api-tester
ln -s ../frontend-test debug/frontend-test
```

### 手动同步
```bash
# 更新API测试工具
cp -r api-tester/* debug/api-tester/

# 更新前端测试页面
cp -r frontend-test/* debug/frontend-test/
```

## 🐛 故障排除

### 1. 404错误
- 确保Django的URL配置正确
- 检查静态文件服务配置
- 确认文件路径正确

### 2. CORS错误
- 检查Django的CORS配置
- 确认API请求使用相同域名

### 3. OAuth重定向错误
- 确认Google Cloud Console中的重定向URI包含debug路径
- 检查JavaScript中的重定向URI配置

## 📝 维护说明

1. **定期同步**: 当api-tester或frontend-test有更新时，同步到debug目录
2. **配置检查**: 确保环境检测逻辑正确工作
3. **路径更新**: 如果Django URL结构变化，更新相应的路径配置
4. **权限管理**: 考虑在生产环境中禁用debug路由

---

这种部署方式完美解决了端口冲突和OAuth重定向问题，同时提供了统一的访问入口。
