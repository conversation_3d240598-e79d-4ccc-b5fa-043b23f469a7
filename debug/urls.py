"""
Debug工具的URL配置
"""

from django.urls import path, re_path
from . import views

app_name = 'debug'

urlpatterns = [
    # Debug工具首页
    path('', views.debug_index, name='index'),
    
    # API状态检查
    path('status/', views.api_status, name='api_status'),
    
    # API测试工具
    path('api-tester/', views.api_tester_index, name='api_tester_index'),
    path('api-tester/manual/', views.api_tester_manual, name='api_tester_manual'),
    
    # 前端测试页面
    path('frontend-test/', views.frontend_test_index, name='frontend_test_index'),
    
    # 静态资源服务
    # API测试工具资源
    re_path(r'^api-tester/(?P<asset_path>css/.+)$', views.serve_debug_asset, name='api_tester_css'),
    re_path(r'^api-tester/(?P<asset_path>js/.+)$', views.serve_debug_asset, name='api_tester_js'),
    re_path(r'^api-tester/(?P<asset_path>config/.+)$', views.serve_debug_asset, name='api_tester_config'),
    
    # 前端测试页面资源
    re_path(r'^frontend-test/(?P<asset_path>.+\.(css|js|json|png|jpg|gif|ico))$', views.serve_debug_asset, name='frontend_test_assets'),
    
    # 通用资源服务（兜底）
    re_path(r'^(?P<asset_path>.+)$', views.serve_debug_asset, name='serve_asset'),
]
