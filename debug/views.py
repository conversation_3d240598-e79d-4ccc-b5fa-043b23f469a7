"""
Debug工具的Django视图
用于服务API测试工具和前端测试页面
"""

from django.shortcuts import render
from django.http import HttpResponse, Http404
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import os
import mimetypes
from pathlib import Path


def debug_index(request):
    """Debug工具首页"""
    current_domain = request.get_host()
    api_base_url = request.build_absolute_uri('/api/v1/')

    # 直接返回HTML，不依赖模板系统
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐛 Mokta Debug 工具集合</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}

        header {{
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }}

        header h1 {{
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}

        .subtitle {{
            font-size: 1.2em;
            opacity: 0.9;
        }}

        .tools-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }}

        .tool-card {{
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
        }}

        .tool-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }}

        .tool-icon {{
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }}

        .tool-name {{
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }}

        .tool-description {{
            color: #6c757d;
            line-height: 1.5;
        }}

        .info-section {{
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }}

        .info-section h2 {{
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }}

        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}

        .info-item {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }}

        .info-label {{
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }}

        .info-value {{
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #6c757d;
            word-break: break-all;
        }}

        footer {{
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }}

        @media (max-width: 768px) {{
            header h1 {{
                font-size: 2em;
            }}

            .tools-grid {{
                grid-template-columns: 1fr;
            }}

            .tool-card {{
                padding: 20px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🐛 Mokta Debug 工具集合</h1>
            <p class="subtitle">开发和测试工具，部署在开发服务器子路径下</p>
        </header>

        <div class="tools-grid">
            <a href="/debug/api-tester/" class="tool-card">
                <span class="tool-icon">🧪</span>
                <div class="tool-name">API测试工具（自动化）</div>
                <div class="tool-description">基于API文档的自动化测试，支持批量测试所有端点</div>
            </a>

            <a href="/debug/api-tester/manual/" class="tool-card">
                <span class="tool-icon">🛠️</span>
                <div class="tool-name">API测试工具（手动）</div>
                <div class="tool-description">类似Postman的手动API测试工具，支持完全自定义请求</div>
            </a>

            <a href="/debug/frontend-test/" class="tool-card">
                <span class="tool-icon">📱</span>
                <div class="tool-name">前端测试页面</div>
                <div class="tool-description">完整的前端功能测试页面，包括文件上传、角色生成等</div>
            </a>
        </div>

        <div class="info-section">
            <h2>🌍 环境信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">当前域名</div>
                    <div class="info-value">{current_domain}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">API基础URL</div>
                    <div class="info-value">{api_base_url}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">环境类型</div>
                    <div class="info-value">
                        {'开发服务器环境' if 'sealos.io' in current_domain else '本地开发环境' if 'localhost' in current_domain else '生产环境'}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">OAuth重定向</div>
                    <div class="info-value">{request.build_absolute_uri('/debug/api-tester/')}</div>
                </div>
            </div>
        </div>

        <footer>
            <p>&copy; 2024 Mokta Debug 工具集合 - 部署在开发服务器子路径</p>
            <p>访问地址：{request.build_absolute_uri('/debug/')}</p>
        </footer>
    </div>

    <script>
        // 自动检查API状态
        fetch('/debug/status/')
            .then(response => response.json())
            .then(data => {{
                console.log('Debug环境状态:', data);
            }})
            .catch(error => {{
                console.warn('无法获取debug状态:', error);
            }});
    </script>
</body>
</html>
    """

    return HttpResponse(html_content, content_type='text/html')


def api_tester_index(request):
    """API测试工具首页（自动化）"""
    return serve_static_file(request, 'api-tester/index.html')


def api_tester_manual(request):
    """API测试工具手动模式"""
    return serve_static_file(request, 'api-tester/manual.html')


def frontend_test_index(request):
    """前端测试页面"""
    return serve_static_file(request, 'frontend-test/index.html')


def serve_static_file(request, file_path):
    """服务静态文件"""
    # 获取debug目录的绝对路径
    debug_dir = Path(__file__).parent
    full_path = debug_dir / file_path
    
    # 检查文件是否存在
    if not full_path.exists() or not full_path.is_file():
        raise Http404(f"File not found: {file_path}")
    
    # 检查文件是否在debug目录内（安全检查）
    try:
        full_path.resolve().relative_to(debug_dir.resolve())
    except ValueError:
        raise Http404("Access denied")
    
    # 读取文件内容
    try:
        with open(full_path, 'rb') as f:
            content = f.read()
    except IOError:
        raise Http404("Cannot read file")
    
    # 确定MIME类型
    content_type, _ = mimetypes.guess_type(str(full_path))
    if content_type is None:
        content_type = 'application/octet-stream'
    
    # 对于HTML文件，注入环境配置
    if content_type == 'text/html':
        content = inject_environment_config(request, content.decode('utf-8'))
        content = content.encode('utf-8')
    
    response = HttpResponse(content, content_type=content_type)
    
    # 添加缓存控制头（开发环境不缓存）
    if settings.DEBUG:
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
    
    return response


def inject_environment_config(request, html_content):
    """在HTML中注入环境配置"""
    # 构建环境配置JavaScript
    config_script = f"""
    <script>
        // 自动注入的环境配置
        window.DEBUG_CONFIG = {{
            apiBaseUrl: '{request.build_absolute_uri("/api/v1/")}',
            currentDomain: '{request.get_host()}',
            isDebugMode: true,
            oauthRedirectUri: '{request.build_absolute_uri("/debug/api-tester/")}',
            environment: '{get_environment_from_domain(request.get_host())}'
        }};
        
        // 覆盖环境检测函数
        if (typeof detectEnvironment === 'function') {{
            window.originalDetectEnvironment = detectEnvironment;
        }}
        
        function detectEnvironment() {{
            return window.DEBUG_CONFIG.environment;
        }}
        
        // 覆盖API基础URL获取函数
        function getApiBaseUrl() {{
            return window.DEBUG_CONFIG.apiBaseUrl.replace('/api/v1/', '');
        }}
    </script>
    """
    
    # 在</head>标签前插入配置脚本
    if '</head>' in html_content:
        html_content = html_content.replace('</head>', config_script + '\n</head>')
    else:
        # 如果没有</head>标签，在<body>前插入
        html_content = config_script + '\n' + html_content
    
    return html_content


def get_environment_from_domain(domain):
    """根据域名确定环境"""
    if 'sealos.io' in domain:
        return 'development'
    elif 'localhost' in domain or '127.0.0.1' in domain:
        return 'local'
    else:
        return 'production'


@csrf_exempt
@require_http_methods(["GET"])
def serve_debug_asset(request, asset_path):
    """服务debug资源文件（CSS, JS, JSON等）"""
    return serve_static_file(request, asset_path)


def api_status(request):
    """API状态检查"""
    from django.http import JsonResponse
    
    status_info = {
        'status': 'ok',
        'environment': get_environment_from_domain(request.get_host()),
        'api_base_url': request.build_absolute_uri('/api/v1/'),
        'debug_tools': {
            'api_tester_auto': request.build_absolute_uri('/debug/api-tester/'),
            'api_tester_manual': request.build_absolute_uri('/debug/api-tester/manual/'),
            'frontend_test': request.build_absolute_uri('/debug/frontend-test/'),
        },
        'oauth_config': {
            'google_client_id': getattr(settings, 'GOOGLE_OAUTH_CLIENT_ID', 'not_configured'),
            'redirect_uri': request.build_absolute_uri('/debug/api-tester/'),
        }
    }
    
    return JsonResponse(status_info)
