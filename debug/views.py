"""
Debug工具的Django视图
用于服务API测试工具和前端测试页面
"""

from django.shortcuts import render
from django.http import HttpResponse, Http404
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import os
import mimetypes
from pathlib import Path


def debug_index(request):
    """Debug工具首页"""
    context = {
        'tools': [
            {
                'name': 'API测试工具（自动化）',
                'description': '基于API文档的自动化测试，支持批量测试',
                'url': '/debug/api-tester/',
                'icon': '🧪'
            },
            {
                'name': 'API测试工具（手动）',
                'description': '类似Postman的手动API测试工具',
                'url': '/debug/api-tester/manual/',
                'icon': '🛠️'
            },
            {
                'name': '前端测试页面',
                'description': '完整的前端功能测试页面',
                'url': '/debug/frontend-test/',
                'icon': '📱'
            }
        ],
        'api_base_url': request.build_absolute_uri('/api/v1/'),
        'current_domain': request.get_host(),
    }
    return render(request, 'debug/index.html', context)


def api_tester_index(request):
    """API测试工具首页（自动化）"""
    return serve_static_file(request, 'api-tester/index.html')


def api_tester_manual(request):
    """API测试工具手动模式"""
    return serve_static_file(request, 'api-tester/manual.html')


def frontend_test_index(request):
    """前端测试页面"""
    return serve_static_file(request, 'frontend-test/index.html')


def serve_static_file(request, file_path):
    """服务静态文件"""
    # 获取debug目录的绝对路径
    debug_dir = Path(__file__).parent
    full_path = debug_dir / file_path
    
    # 检查文件是否存在
    if not full_path.exists() or not full_path.is_file():
        raise Http404(f"File not found: {file_path}")
    
    # 检查文件是否在debug目录内（安全检查）
    try:
        full_path.resolve().relative_to(debug_dir.resolve())
    except ValueError:
        raise Http404("Access denied")
    
    # 读取文件内容
    try:
        with open(full_path, 'rb') as f:
            content = f.read()
    except IOError:
        raise Http404("Cannot read file")
    
    # 确定MIME类型
    content_type, _ = mimetypes.guess_type(str(full_path))
    if content_type is None:
        content_type = 'application/octet-stream'
    
    # 对于HTML文件，注入环境配置
    if content_type == 'text/html':
        content = inject_environment_config(request, content.decode('utf-8'))
        content = content.encode('utf-8')
    
    response = HttpResponse(content, content_type=content_type)
    
    # 添加缓存控制头（开发环境不缓存）
    if settings.DEBUG:
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
    
    return response


def inject_environment_config(request, html_content):
    """在HTML中注入环境配置"""
    # 构建环境配置JavaScript
    config_script = f"""
    <script>
        // 自动注入的环境配置
        window.DEBUG_CONFIG = {{
            apiBaseUrl: '{request.build_absolute_uri("/api/v1/")}',
            currentDomain: '{request.get_host()}',
            isDebugMode: true,
            oauthRedirectUri: '{request.build_absolute_uri("/debug/api-tester/")}',
            environment: '{get_environment_from_domain(request.get_host())}'
        }};
        
        // 覆盖环境检测函数
        if (typeof detectEnvironment === 'function') {{
            window.originalDetectEnvironment = detectEnvironment;
        }}
        
        function detectEnvironment() {{
            return window.DEBUG_CONFIG.environment;
        }}
        
        // 覆盖API基础URL获取函数
        function getApiBaseUrl() {{
            return window.DEBUG_CONFIG.apiBaseUrl.replace('/api/v1/', '');
        }}
    </script>
    """
    
    # 在</head>标签前插入配置脚本
    if '</head>' in html_content:
        html_content = html_content.replace('</head>', config_script + '\n</head>')
    else:
        # 如果没有</head>标签，在<body>前插入
        html_content = config_script + '\n' + html_content
    
    return html_content


def get_environment_from_domain(domain):
    """根据域名确定环境"""
    if 'sealos.io' in domain:
        return 'development'
    elif 'localhost' in domain or '127.0.0.1' in domain:
        return 'local'
    else:
        return 'production'


@csrf_exempt
@require_http_methods(["GET"])
def serve_debug_asset(request, asset_path):
    """服务debug资源文件（CSS, JS, JSON等）"""
    return serve_static_file(request, asset_path)


def api_status(request):
    """API状态检查"""
    from django.http import JsonResponse
    
    status_info = {
        'status': 'ok',
        'environment': get_environment_from_domain(request.get_host()),
        'api_base_url': request.build_absolute_uri('/api/v1/'),
        'debug_tools': {
            'api_tester_auto': request.build_absolute_uri('/debug/api-tester/'),
            'api_tester_manual': request.build_absolute_uri('/debug/api-tester/manual/'),
            'frontend_test': request.build_absolute_uri('/debug/frontend-test/'),
        },
        'oauth_config': {
            'google_client_id': getattr(settings, 'GOOGLE_OAUTH_CLIENT_ID', 'not_configured'),
            'redirect_uri': request.build_absolute_uri('/debug/api-tester/'),
        }
    }
    
    return JsonResponse(status_info)
