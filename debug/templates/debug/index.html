<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐛 Mokta Debug 工具集合</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .tool-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }

        .tool-name {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .tool-description {
            color: #6c757d;
            line-height: 1.5;
        }

        .info-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .info-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #6c757d;
            word-break: break-all;
        }

        .features {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .features h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 5px 0;
            color: #1976d2;
        }

        .features li:before {
            content: "✅ ";
            margin-right: 8px;
        }

        footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            header h1 {
                font-size: 2em;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .tool-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🐛 Mokta Debug 工具集合</h1>
            <p class="subtitle">开发和测试工具，部署在开发服务器子路径下</p>
        </header>

        <div class="tools-grid">
            {% for tool in tools %}
            <a href="{{ tool.url }}" class="tool-card">
                <span class="tool-icon">{{ tool.icon }}</span>
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-description">{{ tool.description }}</div>
            </a>
            {% endfor %}
        </div>

        <div class="info-section">
            <h2>🌍 环境信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">当前域名</div>
                    <div class="info-value">{{ current_domain }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">API基础URL</div>
                    <div class="info-value">{{ api_base_url }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">环境类型</div>
                    <div class="info-value">
                        {% if 'sealos.io' in current_domain %}
                            开发服务器环境
                        {% elif 'localhost' in current_domain %}
                            本地开发环境
                        {% else %}
                            生产环境
                        {% endif %}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">OAuth重定向</div>
                    <div class="info-value">{{ request.build_absolute_uri }}/debug/api-tester/</div>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h2>✨ 主要优势</h2>
            <div class="features">
                <h3>🔗 统一域名访问</h3>
                <ul>
                    <li>使用相同域名，避免CORS跨域问题</li>
                    <li>Google OAuth重定向URI自动匹配</li>
                    <li>无需额外端口配置</li>
                </ul>
            </div>
            
            <div class="features">
                <h3>🛠️ 开发友好</h3>
                <ul>
                    <li>通过Django直接服务，无需启动额外服务</li>
                    <li>支持热更新，修改文件后刷新即可</li>
                    <li>自动环境检测和配置</li>
                </ul>
            </div>
            
            <div class="features">
                <h3>🔐 认证集成</h3>
                <ul>
                    <li>真实Google OAuth登录</li>
                    <li>JWT Token自动管理</li>
                    <li>支持手动Token输入</li>
                </ul>
            </div>
        </div>

        <div class="info-section">
            <h2>📋 使用说明</h2>
            <div style="color: #495057; line-height: 1.6;">
                <p><strong>1. API测试工具（自动化）</strong>：基于API文档自动生成测试用例，支持批量测试所有端点。</p>
                <p><strong>2. API测试工具（手动）</strong>：类似Postman的体验，支持完全自定义的API请求构建。</p>
                <p><strong>3. 前端测试页面</strong>：完整的前端功能测试，包括文件上传、角色生成等。</p>
                <br>
                <p><strong>💡 提示</strong>：所有工具都支持Google OAuth登录，获取真实JWT Token进行API测试。</p>
            </div>
        </div>

        <footer>
            <p>&copy; 2024 Mokta Debug 工具集合 - 部署在开发服务器子路径</p>
            <p>访问地址：{{ request.build_absolute_uri }}</p>
        </footer>
    </div>

    <script>
        // 自动检查API状态
        fetch('/debug/status/')
            .then(response => response.json())
            .then(data => {
                console.log('Debug环境状态:', data);
            })
            .catch(error => {
                console.warn('无法获取debug状态:', error);
            });
    </script>
</body>
</html>
