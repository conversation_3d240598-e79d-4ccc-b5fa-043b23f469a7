{"environments": {"local": {"name": "本地开发环境", "apiBaseUrl": "http://localhost:8000", "testServerPort": 8000, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com", "redirectUri": "http://localhost:8000/debug/api-tester/"}, "features": {"manualMode": true, "allowCustomRequests": true, "showAdvancedOptions": true}}, "development": {"name": "开发服务器环境", "apiBaseUrl": "https://pqqjrlgoetaa.usw.sealos.io", "testServerPort": 8080, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com", "redirectUri": "https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/"}, "features": {"manualMode": true, "allowCustomRequests": true, "showAdvancedOptions": true}}, "production": {"name": "生产环境", "apiBaseUrl": "https://pqqjrlgoetaa.usw.sealos.io", "testServerPort": 3004, "googleOAuth": {"clientId": "930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com", "redirectUri": "https://pqqjrlgoetaa.usw.sealos.io"}, "features": {"manualMode": true, "allowCustomRequests": true, "showAdvancedOptions": false}}}, "defaultEnvironment": "local", "manualTestingOptions": {"defaultHeaders": {"Content-Type": "application/json", "Accept": "application/json"}, "commonTestData": {"validImageFile": {"file_name": "test-image.jpg", "file_type": "image/jpeg", "file_size": 1024000}, "characterGeneration": {"source_photo_key": "test-photo-key-123", "quality": "high"}, "userUpdate": {"nickname": "测试用户", "avatar_url": "https://example.com/avatar.jpg"}}, "instructions": ["手动API测试模式说明：", "1. 支持完全自定义的API请求构建", "2. 可以手动输入请求参数、头部、请求体", "3. 支持保存和重用常用的测试配置", "4. 类似Postman的使用体验", "5. 支持Google OAuth登录获取真实JWT Token"]}}