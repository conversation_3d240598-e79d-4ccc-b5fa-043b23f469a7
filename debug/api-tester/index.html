<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mokta API 测试程序</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <link rel="stylesheet" href="/debug/api-tester/css/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧪 Mokta API 测试程序</h1>
            <p class="subtitle">支持手动和自动API测试，集成Google OAuth登录</p>
        </header>

        <!-- 环境信息区域 -->
        <section class="environment-section">
            <div id="environmentInfo" style="display: none;"></div>
        </section>

        <!-- 登录状态区域 -->
        <section class="login-section">
            <h2>👤 登录状态</h2>
            <div id="loginStatus" class="status info">
                未登录 - 请先进行登录
            </div>
            
            <!-- Google 登录按钮 -->
            <div id="googleLogin" class="google-login">
                <div id="g_id_onload"
                     data-client_id="************-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com"
                     data-callback="handleCredentialResponse"
                     data-auto_prompt="false">
                </div>
                <div class="g_id_signin"
                     data-type="standard"
                     data-size="large"
                     data-theme="outline"
                     data-text="sign_in_with"
                     data-shape="rectangular"
                     data-logo_alignment="left">
                </div>
            </div>

            <!-- 模拟登录按钮 (本地环境) -->
            <div id="mockLogin" class="mock-login" style="display: none;">
                <!-- 内容将由JavaScript动态生成 -->
            </div>
            
            <!-- 用户信息显示 -->
            <div id="userInfo" class="user-info" style="display: none;">
                <h3>✅ 登录成功</h3>
                <p><strong>邮箱:</strong> <span id="userEmail"></span></p>
                <p><strong>昵称:</strong> <span id="userNickname"></span></p>
                <button id="logoutBtn" class="btn btn-secondary">退出登录</button>
            </div>
        </section>

        <!-- 状态消息 -->
        <div id="statusMessage" class="status info" style="display: none;"></div>

        <!-- API测试区域 -->
        <section id="testingArea" class="testing-section" style="display: none;">
            <h2>🔧 API 测试控制台</h2>
            
            <!-- 端点选择 -->
            <div class="control-group">
                <label for="endpointSelector">选择API端点:</label>
                <select id="endpointSelector" class="form-control">
                    <option value="">选择要测试的API端点</option>
                </select>
            </div>

            <!-- 端点详情显示 -->
            <div id="endpointDetails" class="endpoint-details" style="display: none;"></div>

            <!-- 测试控制按钮 -->
            <div class="control-buttons">
                <button id="testSingleBtn" class="btn btn-primary">🧪 测试选中的API</button>
                <button id="testAllBtn" class="btn btn-warning">🚀 批量测试所有API</button>
                <button id="clearResultsBtn" class="btn btn-secondary">🗑️ 清除结果</button>
                <button id="exportResultsBtn" class="btn btn-success">📥 导出结果</button>
            </div>
        </section>

        <!-- 测试结果区域 -->
        <section class="results-section">
            <h2>📊 测试结果</h2>
            <div id="testResults" class="test-results">
                <!-- 测试结果将动态插入这里 -->
            </div>
            <div id="noResults" class="no-results">
                <p>暂无测试结果</p>
                <p class="hint">请先登录并选择API端点进行测试</p>
            </div>
        </section>

        <!-- 使用说明 -->
        <section class="help-section">
            <h2>📖 使用说明</h2>
            <div class="help-content">
                <div class="step">
                    <span class="step-number">1</span>
                    <div class="step-content">
                        <h4>Google 登录</h4>
                        <p>点击上方的Google登录按钮，使用您的Google账号登录以获取JWT Token</p>
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    <div class="step-content">
                        <h4>选择API端点</h4>
                        <p>从下拉菜单中选择要测试的API端点，系统会自动显示端点详情</p>
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    <div class="step-content">
                        <h4>执行测试</h4>
                        <p>点击"测试选中的API"进行单个测试，或"批量测试所有API"进行全面测试</p>
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">4</span>
                    <div class="step-content">
                        <h4>查看结果</h4>
                        <p>测试结果会实时显示，包括响应时间、状态码、响应内容等详细信息</p>
                    </div>
                </div>
            </div>

            <div class="features">
                <h3>✨ 主要功能</h3>
                <ul>
                    <li><strong>自动登录:</strong> 集成Google OAuth，自动获取JWT Token</li>
                    <li><strong>智能测试:</strong> 自动识别API端点，使用预设测试数据</li>
                    <li><strong>实时结果:</strong> 显示响应时间、状态码、详细响应内容</li>
                    <li><strong>批量测试:</strong> 支持一键测试所有API端点</li>
                    <li><strong>结果导出:</strong> 支持将测试结果导出为JSON文件</li>
                    <li><strong>错误处理:</strong> 详细的错误信息和异常处理</li>
                </ul>
            </div>

            <div class="api-info">
                <h3>🔗 API 信息</h3>
                <p><strong>Base URL:</strong> <code>https://pqqjrlgoetaa.usw.sealos.io</code></p>
                <p><strong>API 版本:</strong> <code>v1</code></p>
                <p><strong>认证方式:</strong> <code>JWT Bearer Token</code></p>
                <p><strong>支持的端点:</strong></p>
                <ul>
                    <li>🔐 认证与授权 (Google/Apple OAuth)</li>
                    <li>👤 用户管理 (获取/更新/删除用户信息)</li>
                    <li>📤 文件上传 (预签名URL)</li>
                    <li>🎭 角色管理 (生成/查询/管理3D角色)</li>
                    <li>⚙️ 任务管理 (查询任务状态)</li>
                    <li>🎬 动作库 (获取动作列表)</li>
                </ul>
            </div>
        </section>

        <footer>
            <p>&copy; 2024 Mokta API 测试程序 - 基于您的API文档自动生成</p>
        </footer>
    </div>

    <script src="/debug/api-tester/js/api-tester.js"></script>
</body>
</html>
