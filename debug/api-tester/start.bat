@echo off
chcp 65001 >nul
title Mokta API 测试程序启动器

echo 🧪 Mokta API 测试程序启动器
echo ==================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python
    pause
    exit /b 1
)

REM 获取当前目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo 📁 工作目录: %SCRIPT_DIR%
echo 🌐 端口: 8080
echo 🔗 访问地址: http://localhost:8080
echo ==================================

REM 检查必要文件
set "missing_files="
if not exist "index.html" set "missing_files=%missing_files% index.html"
if not exist "js\api-tester.js" set "missing_files=%missing_files% js\api-tester.js"
if not exist "css\styles.css" set "missing_files=%missing_files% css\styles.css"
if not exist "config\api-endpoints.json" set "missing_files=%missing_files% config\api-endpoints.json"

if not "%missing_files%"=="" (
    echo ❌ 缺少必要文件:
    for %%f in (%missing_files%) do echo    - %%f
    echo.
    echo 请确保所有文件都已正确创建。
    pause
    exit /b 1
)

echo ✅ 所有必要文件检查完成

REM 尝试使用Python启动脚本
if exist "start-server.py" (
    echo 🚀 使用 Python 启动脚本...
    python start-server.py
) else (
    echo 🚀 使用 Python 内置服务器...
    echo 📱 请在浏览器中访问: http://localhost:8080
    echo 🛑 按 Ctrl+C 停止服务器
    echo ==================================
    
    REM 自动打开浏览器
    start "" "http://localhost:8080"
    echo 🌐 已自动打开浏览器
    
    REM 启动Python内置服务器
    python -m http.server 8080
)

pause
