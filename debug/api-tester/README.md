# 🧪 Mokta API 测试程序

一个功能强大的API测试工具，专为Mokta后端系统设计，支持手动和自动API测试，集成Google OAuth登录。

## ✨ 主要功能

- **🔐 自动登录**: 集成Google OAuth，自动获取JWT Token
- **🧪 智能测试**: 自动识别API端点，使用预设测试数据
- **⚡ 实时结果**: 显示响应时间、状态码、详细响应内容
- **🚀 批量测试**: 支持一键测试所有API端点
- **📊 结果导出**: 支持将测试结果导出为JSON文件
- **🛠️ 错误处理**: 详细的错误信息和异常处理
- **📱 响应式设计**: 支持桌面和移动设备

## 🚀 快速开始

### 1. 选择运行环境

程序支持三种环境模式，会自动检测并适配：

#### 🏠 本地环境 (localhost)
- **优势**: 避免Google OAuth重定向问题
- **认证**: 使用模拟登录，无需真实OAuth
- **API目标**: 本地后端服务 (http://localhost:8000)
- **适用场景**: 本地开发和调试

#### 🔧 开发环境 (开发服务器)
- **认证**: 真实Google OAuth登录
- **API目标**: 开发服务器 (https://pqqjrlgoetaa.usw.sealos.io)
- **适用场景**: 完整功能测试

#### 🚀 生产环境 (生产服务器)
- **认证**: 真实Google OAuth登录
- **API目标**: 生产服务器
- **适用场景**: 生产环境验证（谨慎使用）

### 2. 环境切换（可选）

```bash
# 查看当前环境并切换
cd api-tester
python3 switch-env.py

# 直接切换到指定环境
python3 switch-env.py local      # 本地环境
python3 switch-env.py development # 开发环境
python3 switch-env.py production  # 生产环境
```

### 3. 启动测试程序

```bash
# 方法1：使用Python启动脚本（推荐，自动选择端口）
cd api-tester
python3 start-server.py

# 方法2：使用Shell脚本 (Linux/macOS)
./start.sh

# 方法3：使用批处理文件 (Windows)
start.bat
```

### 4. 访问测试页面

程序会自动选择可用端口并打开浏览器，通常是：
```
http://localhost:3001  # 或其他可用端口
```

### 5. 开始测试

#### 本地环境:
1. **模拟登录** - 点击"模拟登录"按钮获取测试Token
2. **选择API** - 从下拉菜单选择要测试的API端点
3. **执行测试** - 点击测试按钮进行测试

#### 开发/生产环境:
1. **Google 登录** - 点击Google登录按钮获取真实JWT Token
2. **选择API** - 从下拉菜单选择要测试的API端点
3. **执行测试** - 点击测试按钮进行单个或批量测试
4. **查看结果** - 实时查看测试结果和详细信息

## 📋 支持的API端点

### 🔐 认证与授权
- `POST /api/v1/login/google` - Google OAuth 登录
- `POST /api/v1/login/apple` - Apple OAuth 登录

### 👤 用户管理
- `GET /api/v1/users/me` - 获取用户信息
- `PUT /api/v1/users/me` - 更新用户信息
- `DELETE /api/v1/users/me/delete` - 删除用户账号
- `GET /api/v1/users/me/usage/today` - 获取今日使用情况

### 📤 文件上传
- `POST /api/v1/upload/presigned-url` - 获取预签名上传URL

### 🎭 角色管理
- `POST /api/v1/characters/generate` - 创建角色生成任务
- `GET /api/v1/characters/` - 获取用户角色列表
- `GET /api/v1/characters/{id}` - 获取角色详情
- `PUT /api/v1/characters/{id}` - 更新角色
- `DELETE /api/v1/characters/{id}` - 删除角色

### ⚙️ 任务管理
- `GET /api/v1/tasks/{id}/status` - 查询任务状态

### 🎬 动作库
- `GET /api/v1/motions/` - 获取所有动作列表

## 🔧 配置说明

### API配置文件
`config/api-endpoints.json` 包含所有API端点的配置信息：

```json
{
  "baseUrl": "https://pqqjrlgoetaa.usw.sealos.io",
  "apiVersion": "v1",
  "endpoints": {
    "category": {
      "endpoint_name": {
        "method": "GET|POST|PUT|DELETE",
        "path": "/api/v1/path",
        "requiresAuth": true|false,
        "description": "端点描述",
        "requestBody": {},
        "expectedResponse": {},
        "testData": {}
      }
    }
  }
}
```

### Google OAuth配置
当前使用的Client ID：
```
930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com
```

## 📊 测试结果

测试结果包含以下信息：
- ✅/❌ 测试状态（成功/失败）
- 🕐 响应时间（毫秒）
- 📊 HTTP状态码
- 📝 详细响应内容
- ⚠️ 错误信息（如果有）
- 🕒 测试时间戳

### 结果导出
点击"导出结果"按钮可将测试结果保存为JSON文件：
```json
{
  "exportTime": "2024-01-15T10:30:00.000Z",
  "totalTests": 10,
  "successCount": 8,
  "failureCount": 2,
  "results": [...]
}
```

## 🛠️ 开发说明

### 文件结构
```
api-tester/
├── index.html              # 主页面
├── css/
│   └── styles.css          # 样式文件
├── js/
│   └── api-tester.js       # 核心JavaScript代码
├── config/
│   └── api-endpoints.json  # API端点配置
├── start-server.py         # 启动脚本
└── README.md              # 说明文档
```

### 核心类：MoktaAPITester
主要方法：
- `init()` - 初始化程序
- `handleGoogleLogin()` - 处理Google登录
- `testSingleEndpoint()` - 测试单个API端点
- `testAllEndpoints()` - 批量测试所有端点
- `executeTest()` - 执行具体的API测试
- `exportResults()` - 导出测试结果

### 添加新的API端点
1. 在 `config/api-endpoints.json` 中添加端点配置
2. 程序会自动识别并添加到测试列表中
3. 可以为端点配置测试数据和预期响应

## 🐛 常见问题

### 1. 端口冲突问题
**问题**: 启动时提示端口被占用
**解决方案**:
- 程序会自动查找可用端口（避开8000, 8080, 8888等常用端口）
- 如仍有冲突，手动指定端口：`python3 -m http.server 3005`

### 2. Google OAuth 重定向问题
**问题**: 本地环境Google登录失败，提示redirect_uri_mismatch
**解决方案**:
- **推荐**: 使用本地环境的模拟登录功能
- **或者**: 在Google Cloud Console中添加 `http://localhost:3001` 到授权重定向URI
- **或者**: 切换到开发环境进行测试

### 3. 环境检测错误
**问题**: 程序检测到错误的环境
**解决方案**:
```bash
# 手动切换环境
python3 switch-env.py local
python3 switch-env.py development
```

### 4. 本地后端连接失败
**问题**: 本地环境下API请求失败
**解决方案**:
- 确保本地后端服务在 http://localhost:8000 运行
- 检查后端CORS配置是否允许测试程序域名
- 或切换到开发环境测试远程API

### 5. 模拟登录无效
**问题**: 本地环境模拟登录后API仍然返回401
**解决方案**:
- 检查后端是否接受测试JWT Token
- 确认后端JWT验证逻辑
- 或使用开发环境进行真实认证测试

### 6. 批量测试过快被限流
**问题**: 批量测试时部分请求失败
**解决方案**:
- 程序已内置500ms延迟
- 如仍被限流，可修改 `api-tester.js` 中的延迟时间
- 或分批进行测试

### 7. 跨域(CORS)错误
**问题**: 浏览器控制台显示CORS错误
**解决方案**:
- 确保后端已正确配置CORS
- 启动脚本会自动添加CORS支持
- 检查后端是否允许测试程序的域名

## 📝 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🔐 集成Google OAuth登录
- 🧪 支持所有Mokta API端点测试
- 📊 实时测试结果显示
- 🚀 批量测试功能
- 📥 测试结果导出功能

## 📄 许可证

本项目基于您的Mokta后端API文档自动生成，仅供内部测试使用。

## 🤝 贡献

如需添加新功能或修复问题：
1. 修改相应的配置文件或代码
2. 测试功能是否正常工作
3. 更新文档说明

---

**注意**: 这是一个专门为Mokta后端系统设计的API测试工具，请确保在安全的环境中使用，不要在生产环境中暴露敏感信息。
