<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mokta API 手动测试工具</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/manual-tester.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🛠️ Mokta API 手动测试工具</h1>
            <p class="subtitle">类似Postman的API测试体验，支持完全自定义请求</p>
        </header>

        <!-- 环境信息区域 -->
        <section class="environment-section">
            <div id="environmentInfo" style="display: none;"></div>
        </section>

        <!-- 登录状态区域 -->
        <section class="login-section">
            <h2>🔐 认证管理</h2>
            <div id="loginStatus" class="status info">
                未登录 - 可以测试公开API或手动输入Token
            </div>
            
            <!-- Google 登录按钮 -->
            <div id="googleLogin" class="google-login">
                <div id="g_id_onload"
                     data-client_id="930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com"
                     data-callback="handleCredentialResponse"
                     data-auto_prompt="false">
                </div>
                <div class="g_id_signin" 
                     data-type="standard" 
                     data-size="large" 
                     data-theme="outline" 
                     data-text="sign_in_with"
                     data-shape="rectangular"
                     data-logo_alignment="left">
                </div>
            </div>
            
            <!-- 用户信息显示 -->
            <div id="userInfo" class="user-info" style="display: none;">
                <h3>✅ 登录成功</h3>
                <p><strong>邮箱:</strong> <span id="userEmail"></span></p>
                <p><strong>昵称:</strong> <span id="userNickname"></span></p>
                <p><strong>JWT Token:</strong> <code id="jwtTokenDisplay" class="token-display">...</code></p>
                <button id="copyTokenBtn" class="btn btn-secondary">📋 复制Token</button>
                <button id="logoutBtn" class="btn btn-secondary">退出登录</button>
            </div>

            <!-- 手动Token输入 -->
            <div class="manual-token-section">
                <h4>🔧 手动Token管理</h4>
                <div class="token-input-group">
                    <input type="text" id="manualTokenInput" class="form-control" 
                           placeholder="粘贴JWT Token或使用上方Google登录获取">
                    <button id="setTokenBtn" class="btn btn-primary">设置Token</button>
                    <button id="clearTokenBtn" class="btn btn-secondary">清除</button>
                </div>
                <p class="token-note">💡 可以从其他工具复制Token，或使用Google登录自动获取</p>
            </div>
        </section>

        <!-- 状态消息 -->
        <div id="statusMessage" class="status info" style="display: none;"></div>

        <!-- 手动API构建区域 -->
        <section class="manual-builder-section">
            <h2>🛠️ API 请求构建器</h2>
            
            <!-- 快速选择预设API -->
            <div class="preset-apis">
                <h3>📋 预设API端点</h3>
                <select id="presetSelector" class="form-control">
                    <option value="">选择预设API端点（可选）</option>
                </select>
                <button id="loadPresetBtn" class="btn btn-info">加载预设</button>
            </div>

            <!-- 请求基本信息 -->
            <div class="request-builder">
                <div class="request-line">
                    <select id="methodSelector" class="method-selector">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                        <option value="PATCH">PATCH</option>
                        <option value="OPTIONS">OPTIONS</option>
                    </select>
                    <input type="text" id="urlInput" class="url-input" 
                           placeholder="输入完整的API URL，例如: https://pqqjrlgoetaa.usw.sealos.io/api/v1/users/me">
                    <button id="sendRequestBtn" class="btn btn-primary send-btn">🚀 发送请求</button>
                </div>
            </div>

            <!-- 请求配置选项卡 -->
            <div class="request-tabs">
                <div class="tab-headers">
                    <button class="tab-header active" data-tab="headers">Headers</button>
                    <button class="tab-header" data-tab="params">Query Params</button>
                    <button class="tab-header" data-tab="body">Request Body</button>
                    <button class="tab-header" data-tab="auth">Auth</button>
                </div>

                <!-- Headers 选项卡 -->
                <div id="headersTab" class="tab-content active">
                    <h4>📝 请求头 (Headers)</h4>
                    <div id="headersContainer" class="key-value-container">
                        <!-- 动态生成的键值对输入框 -->
                    </div>
                    <button id="addHeaderBtn" class="btn btn-secondary">➕ 添加Header</button>
                </div>

                <!-- Query Params 选项卡 -->
                <div id="paramsTab" class="tab-content">
                    <h4>🔍 查询参数 (Query Parameters)</h4>
                    <div id="paramsContainer" class="key-value-container">
                        <!-- 动态生成的键值对输入框 -->
                    </div>
                    <button id="addParamBtn" class="btn btn-secondary">➕ 添加参数</button>
                </div>

                <!-- Request Body 选项卡 -->
                <div id="bodyTab" class="tab-content">
                    <h4>📦 请求体 (Request Body)</h4>
                    <div class="body-type-selector">
                        <label>
                            <input type="radio" name="bodyType" value="none" checked> None
                        </label>
                        <label>
                            <input type="radio" name="bodyType" value="json"> JSON
                        </label>
                        <label>
                            <input type="radio" name="bodyType" value="form"> Form Data
                        </label>
                        <label>
                            <input type="radio" name="bodyType" value="text"> Raw Text
                        </label>
                    </div>
                    
                    <div id="jsonBodyContainer" class="body-container" style="display: none;">
                        <textarea id="jsonBodyInput" class="json-input" 
                                  placeholder='输入JSON格式的请求体，例如:
{
  "nickname": "测试用户",
  "avatar_url": "https://example.com/avatar.jpg"
}'></textarea>
                        <button id="formatJsonBtn" class="btn btn-secondary">🎨 格式化JSON</button>
                    </div>
                    
                    <div id="formBodyContainer" class="body-container" style="display: none;">
                        <div id="formDataContainer" class="key-value-container">
                            <!-- 动态生成的表单数据输入框 -->
                        </div>
                        <button id="addFormDataBtn" class="btn btn-secondary">➕ 添加字段</button>
                    </div>
                    
                    <div id="textBodyContainer" class="body-container" style="display: none;">
                        <textarea id="textBodyInput" class="text-input" 
                                  placeholder="输入原始文本内容"></textarea>
                    </div>
                </div>

                <!-- Auth 选项卡 -->
                <div id="authTab" class="tab-content">
                    <h4>🔐 认证设置</h4>
                    <div class="auth-options">
                        <label>
                            <input type="radio" name="authType" value="none" checked> No Auth
                        </label>
                        <label>
                            <input type="radio" name="authType" value="bearer"> Bearer Token
                        </label>
                        <label>
                            <input type="radio" name="authType" value="basic"> Basic Auth
                        </label>
                    </div>
                    
                    <div id="bearerAuthContainer" class="auth-container" style="display: none;">
                        <input type="text" id="bearerTokenInput" class="form-control" 
                               placeholder="输入Bearer Token（会自动使用登录获取的Token）">
                        <button id="useCurrentTokenBtn" class="btn btn-info">使用当前Token</button>
                    </div>
                    
                    <div id="basicAuthContainer" class="auth-container" style="display: none;">
                        <input type="text" id="basicUsernameInput" class="form-control" 
                               placeholder="用户名">
                        <input type="password" id="basicPasswordInput" class="form-control" 
                               placeholder="密码">
                    </div>
                </div>
            </div>

            <!-- 保存和加载配置 -->
            <div class="config-management">
                <h4>💾 配置管理</h4>
                <div class="config-controls">
                    <input type="text" id="configNameInput" class="form-control" 
                           placeholder="配置名称">
                    <button id="saveConfigBtn" class="btn btn-success">💾 保存配置</button>
                    <select id="savedConfigsSelector" class="form-control">
                        <option value="">选择已保存的配置</option>
                    </select>
                    <button id="loadConfigBtn" class="btn btn-info">📂 加载配置</button>
                    <button id="deleteConfigBtn" class="btn btn-danger">🗑️ 删除配置</button>
                </div>
            </div>
        </section>

        <!-- 响应结果区域 -->
        <section class="response-section">
            <h2>📊 响应结果</h2>
            <div id="responseContainer" class="response-container">
                <div class="no-response">
                    <p>🚀 构建并发送API请求以查看响应结果</p>
                </div>
            </div>
        </section>

        <!-- 测试历史 -->
        <section class="history-section">
            <h2>📜 请求历史</h2>
            <div class="history-controls">
                <button id="clearHistoryBtn" class="btn btn-secondary">🗑️ 清除历史</button>
                <button id="exportHistoryBtn" class="btn btn-success">📥 导出历史</button>
            </div>
            <div id="historyContainer" class="history-container">
                <div class="no-history">
                    <p>暂无请求历史</p>
                </div>
            </div>
        </section>

        <footer>
            <p>&copy; 2024 Mokta API 手动测试工具 - 类似Postman的API测试体验</p>
        </footer>
    </div>

    <script src="js/manual-tester.js"></script>
</body>
</html>
