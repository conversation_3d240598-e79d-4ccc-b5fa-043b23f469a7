#!/usr/bin/env python3
"""
环境切换脚本
允许用户在本地、开发、生产环境之间切换
"""

import json
import sys
from pathlib import Path

def load_environment_config():
    """加载环境配置文件"""
    config_path = Path(__file__).parent / 'config' / 'environment.json'
    
    if not config_path.exists():
        print("❌ 环境配置文件不存在")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载环境配置失败: {e}")
        return None

def save_environment_config(config):
    """保存环境配置文件"""
    config_path = Path(__file__).parent / 'config' / 'environment.json'
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"❌ 保存环境配置失败: {e}")
        return False

def show_current_environment(config):
    """显示当前环境信息"""
    current_env = config.get('defaultEnvironment', 'local')
    env_info = config['environments'][current_env]
    
    print(f"🌍 当前环境: {env_info['name']}")
    print(f"📡 API地址: {env_info['apiBaseUrl']}")
    print(f"🔐 认证模式: {'模拟认证' if env_info['features']['mockAuth'] else 'Google OAuth'}")
    print(f"🌐 测试端口: {env_info['testServerPort']}")

def show_environment_menu(config):
    """显示环境选择菜单"""
    print("\n📋 可用环境:")
    environments = config['environments']
    
    for i, (key, env) in enumerate(environments.items(), 1):
        current = "👈 当前" if key == config.get('defaultEnvironment') else ""
        print(f"  {i}. {env['name']} ({key}) {current}")
        print(f"     API: {env['apiBaseUrl']}")
        print(f"     认证: {'模拟' if env['features']['mockAuth'] else 'OAuth'}")
        print()

def switch_environment(config, env_key):
    """切换环境"""
    if env_key not in config['environments']:
        print(f"❌ 环境 '{env_key}' 不存在")
        return False
    
    config['defaultEnvironment'] = env_key
    
    if save_environment_config(config):
        env_info = config['environments'][env_key]
        print(f"✅ 已切换到环境: {env_info['name']}")
        
        # 显示环境特定的说明
        if env_key == 'local':
            print("\n📝 本地环境说明:")
            print("- 使用模拟认证，避免OAuth重定向问题")
            print("- API请求将发送到本地后端服务")
            print("- 适合本地开发和调试")
        elif env_key == 'development':
            print("\n📝 开发环境说明:")
            print("- 使用真实的Google OAuth认证")
            print("- API请求发送到开发服务器")
            print("- 需要网络连接")
        elif env_key == 'production':
            print("\n📝 生产环境说明:")
            print("- 使用生产环境的API")
            print("- 请谨慎测试，避免影响生产数据")
            print("- 建议只进行只读操作测试")
        
        return True
    
    return False

def main():
    print("🔄 Mokta API 测试程序 - 环境切换工具")
    print("=" * 50)
    
    # 加载配置
    config = load_environment_config()
    if not config:
        sys.exit(1)
    
    # 显示当前环境
    show_current_environment(config)
    
    # 如果有命令行参数，直接切换
    if len(sys.argv) > 1:
        target_env = sys.argv[1].lower()
        if switch_environment(config, target_env):
            print(f"\n🚀 请重新启动API测试程序以应用新环境设置")
        sys.exit(0)
    
    # 交互式菜单
    while True:
        show_environment_menu(config)
        
        try:
            choice = input("请选择环境 (输入数字，或 'q' 退出): ").strip()
            
            if choice.lower() == 'q':
                print("👋 再见！")
                break
            
            choice_num = int(choice)
            env_keys = list(config['environments'].keys())
            
            if 1 <= choice_num <= len(env_keys):
                target_env = env_keys[choice_num - 1]
                
                if target_env == config.get('defaultEnvironment'):
                    print("ℹ️  已经是当前环境")
                    continue
                
                if switch_environment(config, target_env):
                    print(f"\n🚀 请重新启动API测试程序以应用新环境设置")
                    break
            else:
                print("❌ 无效选择，请重试")
                
        except ValueError:
            print("❌ 请输入有效数字")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break

if __name__ == "__main__":
    main()
