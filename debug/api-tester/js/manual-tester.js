/**
 * Mokta API 手动测试工具
 * 类似Postman的API测试体验
 */

class ManualAPITester {
    constructor() {
        this.config = null;
        this.envConfig = null;
        this.currentEnv = 'local';
        this.jwtToken = null;
        this.currentUser = null;
        this.requestHistory = [];
        this.savedConfigs = {};
        
        // 初始化
        this.init();
    }

    async init() {
        try {
            // 检测运行环境
            this.detectEnvironment();
            
            // 加载环境配置
            await this.loadEnvironmentConfig();
            
            // 加载API配置
            await this.loadConfig();
            
            // 显示环境信息
            this.showEnvironmentInfo();
            
            // 初始化Google OAuth
            this.initGoogleOAuth();
            
            // 初始化界面
            this.initUI();
            
            // 绑定事件
            this.bindEvents();
            
            // 加载保存的配置
            this.loadSavedConfigs();
            
            console.log('手动API测试工具初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    detectEnvironment() {
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            this.currentEnv = 'local';
        } else if (hostname.includes('sealos.io') || hostname.includes('dev')) {
            this.currentEnv = 'development';
        } else {
            this.currentEnv = 'production';
        }
        
        console.log(`检测到环境: ${this.currentEnv} (${hostname}:${port})`);
    }

    async loadEnvironmentConfig() {
        try {
            const response = await fetch('/debug/api-tester/config/environment.json');
            if (!response.ok) {
                throw new Error('无法加载环境配置文件');
            }
            const envData = await response.json();
            this.envConfig = envData.environments[this.currentEnv];
            this.fullEnvData = envData;
            
            if (!this.envConfig) {
                throw new Error(`未找到环境配置: ${this.currentEnv}`);
            }
            
            console.log('环境配置加载成功:', this.envConfig);
        } catch (error) {
            throw new Error('加载环境配置失败: ' + error.message);
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('/debug/api-tester/config/api-endpoints.json');
            if (!response.ok) {
                throw new Error('无法加载API配置文件');
            }
            this.config = await response.json();
            
            // 根据环境更新API基础URL
            this.config.baseUrl = this.envConfig.apiBaseUrl;
            
            console.log('API配置加载成功:', this.config);
        } catch (error) {
            throw new Error('加载配置失败: ' + error.message);
        }
    }

    showEnvironmentInfo() {
        const envInfo = document.getElementById('environmentInfo');
        if (envInfo) {
            envInfo.innerHTML = `
                <div class="env-info">
                    <h4>🌍 当前环境: ${this.envConfig.name}</h4>
                    <p><strong>API地址:</strong> ${this.envConfig.apiBaseUrl}</p>
                    <p><strong>测试模式:</strong> 手动构建API请求</p>
                    <p><strong>OAuth重定向:</strong> ${this.envConfig.googleOAuth.redirectUri || '自动配置'}</p>
                </div>
            `;
            envInfo.style.display = 'block';
        }
    }

    initGoogleOAuth() {
        // Google OAuth 回调函数
        window.handleCredentialResponse = (response) => {
            this.handleGoogleLogin(response.credential);
        };
        
        // 更新Google OAuth Client ID
        const gOnload = document.getElementById('g_id_onload');
        if (gOnload) {
            gOnload.setAttribute('data-client_id', this.envConfig.googleOAuth.clientId);
        }
    }

    async handleGoogleLogin(idToken) {
        try {
            this.showStatus('正在进行Google登录验证...', 'info');
            
            const response = await fetch(`${this.config.baseUrl}/api/v1/login/google`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id_token: idToken
                })
            });

            const data = await response.json();
            
            if (response.ok && data.access_token) {
                this.jwtToken = data.access_token;
                this.currentUser = data.user;
                
                this.showLoginSuccess(data);
                this.updateTokenDisplay();
                
            } else {
                throw new Error(data.error || '登录失败');
            }
        } catch (error) {
            console.error('Google登录失败:', error);
            this.showError('登录失败: ' + error.message);
        }
    }

    showLoginSuccess(data) {
        const loginStatus = document.getElementById('loginStatus');
        const userInfo = document.getElementById('userInfo');
        const googleLogin = document.getElementById('googleLogin');
        
        loginStatus.className = 'status success';
        loginStatus.textContent = '✅ 登录成功，JWT Token已获取';
        
        document.getElementById('userEmail').textContent = data.user.email;
        document.getElementById('userNickname').textContent = data.user.nickname || '未设置';
        
        googleLogin.style.display = 'none';
        userInfo.style.display = 'block';
        
        this.showStatus('登录成功，JWT Token已获取', 'success');
    }

    updateTokenDisplay() {
        const tokenDisplay = document.getElementById('jwtTokenDisplay');
        const manualTokenInput = document.getElementById('manualTokenInput');
        const bearerTokenInput = document.getElementById('bearerTokenInput');
        
        if (this.jwtToken) {
            // 显示Token的前20个字符...
            const displayToken = this.jwtToken.length > 50 
                ? this.jwtToken.substring(0, 50) + '...' 
                : this.jwtToken;
            tokenDisplay.textContent = displayToken;
            
            // 同步到手动输入框
            manualTokenInput.value = this.jwtToken;
            bearerTokenInput.value = this.jwtToken;
        }
    }

    initUI() {
        // 初始化预设API选择器
        this.populatePresetSelector();
        
        // 初始化默认的键值对输入框
        this.addKeyValuePair('headersContainer', 'Content-Type', 'application/json');
        this.addKeyValuePair('paramsContainer', '', '');
        this.addKeyValuePair('formDataContainer', '', '');
        
        // 设置默认URL
        document.getElementById('urlInput').value = this.config.baseUrl + '/api/v1/';
    }

    populatePresetSelector() {
        const selector = document.getElementById('presetSelector');
        selector.innerHTML = '<option value="">选择预设API端点（可选）</option>';
        
        Object.keys(this.config.endpoints).forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = this.getCategoryDisplayName(category);
            
            Object.keys(this.config.endpoints[category]).forEach(endpoint => {
                const option = document.createElement('option');
                option.value = `${category}.${endpoint}`;
                const endpointConfig = this.config.endpoints[category][endpoint];
                option.textContent = `${endpointConfig.method} ${endpointConfig.path} - ${endpointConfig.description}`;
                optgroup.appendChild(option);
            });
            
            selector.appendChild(optgroup);
        });
    }

    getCategoryDisplayName(category) {
        const displayNames = {
            'authentication': '🔐 认证与授权',
            'users': '👤 用户管理',
            'uploads': '📤 文件上传',
            'characters': '🎭 角色管理',
            'tasks': '⚙️ 任务管理',
            'motions': '🎬 动作库'
        };
        return displayNames[category] || category;
    }

    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('.tab-header').forEach(header => {
            header.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 请求体类型切换
        document.querySelectorAll('input[name="bodyType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.switchBodyType(e.target.value);
            });
        });

        // 认证类型切换
        document.querySelectorAll('input[name="authType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.switchAuthType(e.target.value);
            });
        });

        // 添加键值对按钮
        document.getElementById('addHeaderBtn').addEventListener('click', () => {
            this.addKeyValuePair('headersContainer');
        });
        
        document.getElementById('addParamBtn').addEventListener('click', () => {
            this.addKeyValuePair('paramsContainer');
        });
        
        document.getElementById('addFormDataBtn').addEventListener('click', () => {
            this.addKeyValuePair('formDataContainer');
        });

        // 发送请求按钮
        document.getElementById('sendRequestBtn').addEventListener('click', () => {
            this.sendRequest();
        });

        // Token管理
        document.getElementById('setTokenBtn').addEventListener('click', () => {
            this.setManualToken();
        });
        
        document.getElementById('clearTokenBtn').addEventListener('click', () => {
            this.clearToken();
        });
        
        document.getElementById('copyTokenBtn').addEventListener('click', () => {
            this.copyToken();
        });

        // 预设API加载
        document.getElementById('loadPresetBtn').addEventListener('click', () => {
            this.loadPresetAPI();
        });

        // JSON格式化
        document.getElementById('formatJsonBtn').addEventListener('click', () => {
            this.formatJSON();
        });

        // 使用当前Token
        document.getElementById('useCurrentTokenBtn').addEventListener('click', () => {
            this.useCurrentToken();
        });

        // 配置管理
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.saveCurrentConfig();
        });
        
        document.getElementById('loadConfigBtn').addEventListener('click', () => {
            this.loadSavedConfig();
        });
        
        document.getElementById('deleteConfigBtn').addEventListener('click', () => {
            this.deleteSavedConfig();
        });

        // 历史管理
        document.getElementById('clearHistoryBtn').addEventListener('click', () => {
            this.clearHistory();
        });
        
        document.getElementById('exportHistoryBtn').addEventListener('click', () => {
            this.exportHistory();
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
    }

    switchTab(tabName) {
        // 隐藏所有选项卡内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // 移除所有选项卡头部的active类
        document.querySelectorAll('.tab-header').forEach(header => {
            header.classList.remove('active');
        });
        
        // 显示选中的选项卡
        document.getElementById(tabName + 'Tab').classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    }

    switchBodyType(type) {
        // 隐藏所有请求体容器
        document.querySelectorAll('.body-container').forEach(container => {
            container.style.display = 'none';
        });
        
        // 显示选中的容器
        if (type !== 'none') {
            document.getElementById(type + 'BodyContainer').style.display = 'block';
        }
    }

    switchAuthType(type) {
        // 隐藏所有认证容器
        document.querySelectorAll('.auth-container').forEach(container => {
            container.style.display = 'none';
        });
        
        // 显示选中的容器
        if (type !== 'none') {
            document.getElementById(type + 'AuthContainer').style.display = 'block';
        }
    }

    addKeyValuePair(containerId, key = '', value = '') {
        const container = document.getElementById(containerId);
        const pairDiv = document.createElement('div');
        pairDiv.className = 'key-value-pair';
        
        pairDiv.innerHTML = `
            <input type="text" class="form-control key-input" placeholder="Key" value="${key}">
            <input type="text" class="form-control value-input" placeholder="Value" value="${value}">
            <button type="button" class="remove-btn" onclick="this.parentElement.remove()">✕</button>
        `;
        
        container.appendChild(pairDiv);
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusMessage');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }

    showError(message) {
        this.showStatus(message, 'error');
    }

    loadSavedConfigs() {
        const saved = localStorage.getItem('mokta-api-configs');
        if (saved) {
            this.savedConfigs = JSON.parse(saved);
            this.updateSavedConfigsSelector();
        }
    }

    updateSavedConfigsSelector() {
        const selector = document.getElementById('savedConfigsSelector');
        selector.innerHTML = '<option value="">选择已保存的配置</option>';

        Object.keys(this.savedConfigs).forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            selector.appendChild(option);
        });
    }

    setManualToken() {
        const tokenInput = document.getElementById('manualTokenInput');
        const token = tokenInput.value.trim();

        if (token) {
            this.jwtToken = token;
            this.updateTokenDisplay();
            this.showStatus('Token已设置', 'success');
        } else {
            this.showError('请输入有效的Token');
        }
    }

    clearToken() {
        this.jwtToken = null;
        this.currentUser = null;

        document.getElementById('manualTokenInput').value = '';
        document.getElementById('bearerTokenInput').value = '';
        document.getElementById('jwtTokenDisplay').textContent = '...';

        // 重置登录状态
        document.getElementById('loginStatus').className = 'status info';
        document.getElementById('loginStatus').textContent = '未登录 - 可以测试公开API或手动输入Token';
        document.getElementById('googleLogin').style.display = 'block';
        document.getElementById('userInfo').style.display = 'none';

        this.showStatus('Token已清除', 'info');
    }

    copyToken() {
        if (this.jwtToken) {
            navigator.clipboard.writeText(this.jwtToken).then(() => {
                this.showStatus('Token已复制到剪贴板', 'success');
            }).catch(() => {
                this.showError('复制失败，请手动复制');
            });
        } else {
            this.showError('没有可复制的Token');
        }
    }

    useCurrentToken() {
        if (this.jwtToken) {
            document.getElementById('bearerTokenInput').value = this.jwtToken;
            document.querySelector('input[name="authType"][value="bearer"]').checked = true;
            this.switchAuthType('bearer');
            this.showStatus('已使用当前Token', 'success');
        } else {
            this.showError('请先登录或手动设置Token');
        }
    }

    loadPresetAPI() {
        const selector = document.getElementById('presetSelector');
        const selectedValue = selector.value;

        if (!selectedValue) {
            this.showError('请先选择一个预设API端点');
            return;
        }

        const [category, endpoint] = selectedValue.split('.');
        const endpointConfig = this.config.endpoints[category][endpoint];

        // 设置方法和URL
        document.getElementById('methodSelector').value = endpointConfig.method;
        document.getElementById('urlInput').value = this.config.baseUrl + endpointConfig.path;

        // 清除现有的参数
        this.clearKeyValuePairs('headersContainer');
        this.clearKeyValuePairs('paramsContainer');
        this.clearKeyValuePairs('formDataContainer');

        // 设置默认Headers
        this.addKeyValuePair('headersContainer', 'Content-Type', 'application/json');
        if (endpointConfig.requiresAuth) {
            this.addKeyValuePair('headersContainer', 'Authorization', `Bearer ${this.jwtToken || 'YOUR_JWT_TOKEN'}`);
        }

        // 设置请求体
        if (endpointConfig.requestBody && ['POST', 'PUT', 'PATCH'].includes(endpointConfig.method)) {
            document.querySelector('input[name="bodyType"][value="json"]').checked = true;
            this.switchBodyType('json');

            const testData = endpointConfig.testData?.valid || endpointConfig.requestBody;
            document.getElementById('jsonBodyInput').value = JSON.stringify(testData, null, 2);
        }

        // 设置查询参数
        if (endpointConfig.queryParams) {
            Object.keys(endpointConfig.queryParams).forEach(param => {
                let defaultValue = '';
                if (param === 'page') defaultValue = '1';
                if (param === 'page_size') defaultValue = '10';
                this.addKeyValuePair('paramsContainer', param, defaultValue);
            });
        }

        this.showStatus(`已加载预设API: ${endpointConfig.description}`, 'success');
    }

    clearKeyValuePairs(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
    }

    formatJSON() {
        const jsonInput = document.getElementById('jsonBodyInput');
        try {
            const parsed = JSON.parse(jsonInput.value);
            jsonInput.value = JSON.stringify(parsed, null, 2);
            this.showStatus('JSON已格式化', 'success');
        } catch (error) {
            this.showError('JSON格式错误: ' + error.message);
        }
    }

    async sendRequest() {
        const method = document.getElementById('methodSelector').value;
        const url = document.getElementById('urlInput').value.trim();

        if (!url) {
            this.showError('请输入API URL');
            return;
        }

        this.showStatus('正在发送请求...', 'info');

        try {
            const requestConfig = this.buildRequestConfig(method, url);
            const startTime = Date.now();

            const response = await fetch(requestConfig.url, requestConfig.options);
            const responseTime = Date.now() - startTime;

            let responseData;
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }

            const result = {
                request: requestConfig,
                response: {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData
                },
                responseTime,
                timestamp: new Date().toISOString(),
                success: response.ok
            };

            this.displayResponse(result);
            this.addToHistory(result);

            this.showStatus(`请求完成 (${responseTime}ms)`, result.success ? 'success' : 'error');

        } catch (error) {
            console.error('请求失败:', error);
            this.showError('请求失败: ' + error.message);

            const errorResult = {
                request: { method, url },
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false
            };

            this.addToHistory(errorResult);
        }
    }

    buildRequestConfig(method, url) {
        const config = {
            url: url,
            options: {
                method: method,
                headers: {}
            }
        };

        // 收集Headers
        const headerPairs = document.querySelectorAll('#headersContainer .key-value-pair');
        headerPairs.forEach(pair => {
            const key = pair.querySelector('.key-input').value.trim();
            const value = pair.querySelector('.value-input').value.trim();
            if (key && value) {
                config.options.headers[key] = value;
            }
        });

        // 处理认证
        const authType = document.querySelector('input[name="authType"]:checked').value;
        if (authType === 'bearer') {
            const token = document.getElementById('bearerTokenInput').value.trim();
            if (token) {
                config.options.headers['Authorization'] = `Bearer ${token}`;
            }
        } else if (authType === 'basic') {
            const username = document.getElementById('basicUsernameInput').value.trim();
            const password = document.getElementById('basicPasswordInput').value.trim();
            if (username && password) {
                const credentials = btoa(`${username}:${password}`);
                config.options.headers['Authorization'] = `Basic ${credentials}`;
            }
        }

        // 处理查询参数
        const paramPairs = document.querySelectorAll('#paramsContainer .key-value-pair');
        const params = new URLSearchParams();
        paramPairs.forEach(pair => {
            const key = pair.querySelector('.key-input').value.trim();
            const value = pair.querySelector('.value-input').value.trim();
            if (key && value) {
                params.append(key, value);
            }
        });

        if (params.toString()) {
            config.url += (config.url.includes('?') ? '&' : '?') + params.toString();
        }

        // 处理请求体
        const bodyType = document.querySelector('input[name="bodyType"]:checked').value;
        if (bodyType !== 'none' && ['POST', 'PUT', 'PATCH'].includes(method)) {
            if (bodyType === 'json') {
                const jsonBody = document.getElementById('jsonBodyInput').value.trim();
                if (jsonBody) {
                    config.options.body = jsonBody;
                    config.options.headers['Content-Type'] = 'application/json';
                }
            } else if (bodyType === 'form') {
                const formData = new FormData();
                const formPairs = document.querySelectorAll('#formDataContainer .key-value-pair');
                formPairs.forEach(pair => {
                    const key = pair.querySelector('.key-input').value.trim();
                    const value = pair.querySelector('.value-input').value.trim();
                    if (key && value) {
                        formData.append(key, value);
                    }
                });
                config.options.body = formData;
                // 不设置Content-Type，让浏览器自动设置
            } else if (bodyType === 'text') {
                const textBody = document.getElementById('textBodyInput').value;
                config.options.body = textBody;
            }
        }

        return config;
    }

    displayResponse(result) {
        const container = document.getElementById('responseContainer');

        const statusClass = result.success ? 'success' : 'error';
        const statusText = `${result.response.status} ${result.response.statusText}`;

        container.innerHTML = `
            <div class="response-header">
                <div>
                    <span class="response-status ${statusClass}">${statusText}</span>
                    <span class="response-time">${result.responseTime}ms</span>
                </div>
                <div>
                    <button onclick="manualTester.copyResponse()" class="btn btn-secondary">📋 复制响应</button>
                </div>
            </div>
            <div class="response-body">
                <h5>响应头:</h5>
                <div class="response-json">${JSON.stringify(result.response.headers, null, 2)}</div>
                <h5>响应体:</h5>
                <div class="response-json">${typeof result.response.data === 'string'
                    ? result.response.data
                    : JSON.stringify(result.response.data, null, 2)}</div>
            </div>
        `;

        this.currentResponse = result;
    }

    copyResponse() {
        if (this.currentResponse) {
            const responseText = JSON.stringify(this.currentResponse.response.data, null, 2);
            navigator.clipboard.writeText(responseText).then(() => {
                this.showStatus('响应已复制到剪贴板', 'success');
            }).catch(() => {
                this.showError('复制失败');
            });
        }
    }

    addToHistory(result) {
        this.requestHistory.unshift(result);

        // 限制历史记录数量
        if (this.requestHistory.length > 50) {
            this.requestHistory = this.requestHistory.slice(0, 50);
        }

        this.updateHistoryDisplay();
    }

    updateHistoryDisplay() {
        const container = document.getElementById('historyContainer');

        if (this.requestHistory.length === 0) {
            container.innerHTML = '<div class="no-history"><p>暂无请求历史</p></div>';
            return;
        }

        const historyHTML = this.requestHistory.map((item, index) => {
            const statusClass = item.success ? 'success' : 'error';
            const time = new Date(item.timestamp).toLocaleTimeString();

            return `
                <div class="history-item" onclick="manualTester.loadFromHistory(${index})">
                    <span class="history-method">${item.request.method || 'ERROR'}</span>
                    <span class="history-url">${item.request.url || 'N/A'}</span>
                    <span class="history-status ${statusClass}">${item.response?.status || 'ERROR'}</span>
                    <span class="history-time">${time}</span>
                </div>
            `;
        }).join('');

        container.innerHTML = historyHTML;
    }

    loadFromHistory(index) {
        const item = this.requestHistory[index];
        if (!item || !item.request) return;

        // 加载请求配置
        if (item.request.method) {
            document.getElementById('methodSelector').value = item.request.method;
        }

        if (item.request.url) {
            // 分离URL和查询参数
            const [baseUrl, queryString] = item.request.url.split('?');
            document.getElementById('urlInput').value = baseUrl;

            // 加载查询参数
            if (queryString) {
                this.clearKeyValuePairs('paramsContainer');
                const params = new URLSearchParams(queryString);
                params.forEach((value, key) => {
                    this.addKeyValuePair('paramsContainer', key, value);
                });
            }
        }

        this.showStatus('已从历史记录加载请求配置', 'success');
    }

    saveCurrentConfig() {
        const configName = document.getElementById('configNameInput').value.trim();
        if (!configName) {
            this.showError('请输入配置名称');
            return;
        }

        const config = {
            method: document.getElementById('methodSelector').value,
            url: document.getElementById('urlInput').value,
            headers: this.getKeyValuePairs('headersContainer'),
            params: this.getKeyValuePairs('paramsContainer'),
            bodyType: document.querySelector('input[name="bodyType"]:checked').value,
            authType: document.querySelector('input[name="authType"]:checked').value,
            timestamp: new Date().toISOString()
        };

        // 保存请求体内容
        if (config.bodyType === 'json') {
            config.jsonBody = document.getElementById('jsonBodyInput').value;
        } else if (config.bodyType === 'form') {
            config.formData = this.getKeyValuePairs('formDataContainer');
        } else if (config.bodyType === 'text') {
            config.textBody = document.getElementById('textBodyInput').value;
        }

        this.savedConfigs[configName] = config;
        localStorage.setItem('mokta-api-configs', JSON.stringify(this.savedConfigs));

        this.updateSavedConfigsSelector();
        document.getElementById('configNameInput').value = '';

        this.showStatus(`配置 "${configName}" 已保存`, 'success');
    }

    getKeyValuePairs(containerId) {
        const pairs = {};
        const pairElements = document.querySelectorAll(`#${containerId} .key-value-pair`);
        pairElements.forEach(pair => {
            const key = pair.querySelector('.key-input').value.trim();
            const value = pair.querySelector('.value-input').value.trim();
            if (key && value) {
                pairs[key] = value;
            }
        });
        return pairs;
    }

    loadSavedConfig() {
        const configName = document.getElementById('savedConfigsSelector').value;
        if (!configName) {
            this.showError('请选择要加载的配置');
            return;
        }

        const config = this.savedConfigs[configName];
        if (!config) {
            this.showError('配置不存在');
            return;
        }

        // 加载基本配置
        document.getElementById('methodSelector').value = config.method || 'GET';
        document.getElementById('urlInput').value = config.url || '';

        // 加载Headers
        this.clearKeyValuePairs('headersContainer');
        Object.entries(config.headers || {}).forEach(([key, value]) => {
            this.addKeyValuePair('headersContainer', key, value);
        });

        // 加载查询参数
        this.clearKeyValuePairs('paramsContainer');
        Object.entries(config.params || {}).forEach(([key, value]) => {
            this.addKeyValuePair('paramsContainer', key, value);
        });

        // 加载请求体类型
        if (config.bodyType) {
            document.querySelector(`input[name="bodyType"][value="${config.bodyType}"]`).checked = true;
            this.switchBodyType(config.bodyType);

            if (config.bodyType === 'json' && config.jsonBody) {
                document.getElementById('jsonBodyInput').value = config.jsonBody;
            } else if (config.bodyType === 'form' && config.formData) {
                this.clearKeyValuePairs('formDataContainer');
                Object.entries(config.formData).forEach(([key, value]) => {
                    this.addKeyValuePair('formDataContainer', key, value);
                });
            } else if (config.bodyType === 'text' && config.textBody) {
                document.getElementById('textBodyInput').value = config.textBody;
            }
        }

        // 加载认证类型
        if (config.authType) {
            document.querySelector(`input[name="authType"][value="${config.authType}"]`).checked = true;
            this.switchAuthType(config.authType);
        }

        this.showStatus(`配置 "${configName}" 已加载`, 'success');
    }

    deleteSavedConfig() {
        const configName = document.getElementById('savedConfigsSelector').value;
        if (!configName) {
            this.showError('请选择要删除的配置');
            return;
        }

        if (confirm(`确定要删除配置 "${configName}" 吗？`)) {
            delete this.savedConfigs[configName];
            localStorage.setItem('mokta-api-configs', JSON.stringify(this.savedConfigs));

            this.updateSavedConfigsSelector();
            this.showStatus(`配置 "${configName}" 已删除`, 'success');
        }
    }

    clearHistory() {
        if (confirm('确定要清除所有请求历史吗？')) {
            this.requestHistory = [];
            this.updateHistoryDisplay();
            this.showStatus('请求历史已清除', 'success');
        }
    }

    exportHistory() {
        if (this.requestHistory.length === 0) {
            this.showError('没有历史记录可导出');
            return;
        }

        const data = {
            exportTime: new Date().toISOString(),
            totalRequests: this.requestHistory.length,
            history: this.requestHistory
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `mokta-api-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showStatus('请求历史已导出', 'success');
    }

    logout() {
        this.clearToken();
        this.showStatus('已退出登录', 'info');
    }
}

// 初始化手动API测试工具
document.addEventListener('DOMContentLoaded', () => {
    window.manualTester = new ManualAPITester();
});
