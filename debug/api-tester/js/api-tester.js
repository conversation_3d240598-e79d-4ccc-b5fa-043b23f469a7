/**
 * Mokta API 测试程序核心引擎
 * 支持手动和自动API测试，集成Google OAuth登录
 */

class MoktaAPITester {
    constructor() {
        this.config = null;
        this.envConfig = null;
        this.currentEnv = 'local';
        this.jwtToken = null;
        this.currentUser = null;
        this.testResults = [];
        this.isRunning = false;

        // 初始化
        this.init();
    }

    async init() {
        try {
            // 检测运行环境
            this.detectEnvironment();

            // 加载环境配置
            await this.loadEnvironmentConfig();

            // 加载API配置
            await this.loadConfig();

            // 显示环境信息
            this.showEnvironmentInfo();

            // 根据环境初始化认证
            if (this.envConfig.features.mockAuth) {
                this.initMockAuth();
            } else {
                this.initGoogleOAuth();
            }

            // 绑定事件
            this.bindEvents();

            console.log('API测试程序初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    detectEnvironment() {
        const hostname = window.location.hostname;
        const port = window.location.port;

        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            this.currentEnv = 'local';
        } else if (hostname.includes('sealos.io') || hostname.includes('dev')) {
            this.currentEnv = 'development';
        } else {
            this.currentEnv = 'production';
        }

        console.log(`检测到环境: ${this.currentEnv} (${hostname}:${port})`);
    }

    async loadEnvironmentConfig() {
        try {
            const response = await fetch('./config/environment.json');
            if (!response.ok) {
                throw new Error('无法加载环境配置文件');
            }
            const envData = await response.json();
            this.envConfig = envData.environments[this.currentEnv];
            this.fullEnvData = envData; // 保存完整的环境数据

            if (!this.envConfig) {
                throw new Error(`未找到环境配置: ${this.currentEnv}`);
            }

            console.log('环境配置加载成功:', this.envConfig);
        } catch (error) {
            throw new Error('加载环境配置失败: ' + error.message);
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('./config/api-endpoints.json');
            if (!response.ok) {
                throw new Error('无法加载API配置文件');
            }
            this.config = await response.json();

            // 根据环境更新API基础URL
            this.config.baseUrl = this.envConfig.apiBaseUrl;

            console.log('API配置加载成功:', this.config);
        } catch (error) {
            throw new Error('加载配置失败: ' + error.message);
        }
    }

    showEnvironmentInfo() {
        const envInfo = document.getElementById('environmentInfo');
        if (envInfo) {
            envInfo.innerHTML = `
                <div class="env-info">
                    <h4>🌍 当前环境: ${this.envConfig.name}</h4>
                    <p><strong>API地址:</strong> ${this.envConfig.apiBaseUrl}</p>
                    <p><strong>认证模式:</strong> ${this.envConfig.features.mockAuth ? '模拟认证' : 'Google OAuth'}</p>
                    ${this.currentEnv === 'local' ? this.getLocalModeInstructions() : ''}
                </div>
            `;
            envInfo.style.display = 'block';
        }
    }

    getLocalModeInstructions() {
        return `
            <div class="local-instructions">
                <h5>📝 本地开发模式说明:</h5>
                <ul>
                    <li>由于Google OAuth的redirect URI限制，本地环境使用模拟登录</li>
                    <li>点击"模拟登录"按钮获取测试Token</li>
                    <li>如需真实OAuth，请在Google Console中添加本地域名到重定向URI</li>
                    <li>或切换到开发/生产环境进行完整测试</li>
                </ul>
            </div>
        `;
    }

    initMockAuth() {
        // 本地环境使用模拟认证
        console.log('初始化模拟认证模式');

        // 隐藏Google登录按钮，显示模拟登录按钮
        const googleLogin = document.getElementById('googleLogin');
        const mockLogin = document.getElementById('mockLogin');

        if (googleLogin) googleLogin.style.display = 'none';
        if (mockLogin) {
            mockLogin.style.display = 'block';
            mockLogin.innerHTML = `
                <button id="mockLoginBtn" class="btn btn-primary">
                    🧪 模拟登录 (本地测试)
                </button>
                <p class="mock-note">本地环境使用模拟认证，避免OAuth重定向问题</p>
            `;

            document.getElementById('mockLoginBtn').addEventListener('click', () => {
                this.handleMockLogin();
            });
        }
    }

    initGoogleOAuth() {
        // Google OAuth 回调函数
        window.handleCredentialResponse = (response) => {
            this.handleGoogleLogin(response.credential);
        };

        // 更新Google OAuth Client ID
        const gOnload = document.getElementById('g_id_onload');
        if (gOnload) {
            gOnload.setAttribute('data-client_id', this.envConfig.googleOAuth.clientId);
        }
    }

    handleMockLogin() {
        try {
            this.showStatus('正在进行模拟登录...', 'info');

            // 使用环境配置中的模拟数据
            const localOptions = this.fullEnvData?.localDevelopmentOptions || {
                mockJwtToken: 'mock_jwt_token_for_testing',
                mockUser: {
                    id: '550e8400-e29b-41d4-a716-************',
                    email: '<EMAIL>',
                    nickname: 'API测试用户',
                    avatar_url: 'https://via.placeholder.com/150'
                }
            };

            this.jwtToken = localOptions.mockJwtToken;
            this.currentUser = localOptions.mockUser;

            const mockData = {
                access_token: this.jwtToken,
                user: this.currentUser
            };

            this.showLoginSuccess(mockData);
            this.enableTestingFeatures();

            this.showStatus('模拟登录成功！可以开始测试API', 'success');

        } catch (error) {
            console.error('模拟登录失败:', error);
            this.showError('模拟登录失败: ' + error.message);
        }
    }

    async handleGoogleLogin(idToken) {
        try {
            this.showStatus('正在进行Google登录验证...', 'info');
            
            const response = await fetch(`${this.config.baseUrl}/api/v1/login/google`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id_token: idToken
                })
            });

            const data = await response.json();
            
            if (response.ok && data.access_token) {
                this.jwtToken = data.access_token;
                this.currentUser = data.user;
                
                this.showLoginSuccess(data);
                this.enableTestingFeatures();
                
                // 自动测试用户信息API
                await this.testSingleEndpoint('users', 'get_profile');
                
            } else {
                throw new Error(data.error || '登录失败');
            }
        } catch (error) {
            console.error('Google登录失败:', error);
            this.showError('登录失败: ' + error.message);
        }
    }

    showLoginSuccess(data) {
        const loginStatus = document.getElementById('loginStatus');
        const userInfo = document.getElementById('userInfo');
        const googleLogin = document.getElementById('googleLogin');
        
        loginStatus.className = 'status success';
        loginStatus.textContent = '✅ 登录成功';
        
        document.getElementById('userEmail').textContent = data.user.email;
        document.getElementById('userNickname').textContent = data.user.nickname || '未设置';
        
        googleLogin.style.display = 'none';
        userInfo.style.display = 'block';
        
        this.showStatus('登录成功，JWT Token已获取', 'success');
    }

    enableTestingFeatures() {
        document.getElementById('testingArea').style.display = 'block';
        this.populateEndpointSelector();
    }

    populateEndpointSelector() {
        const selector = document.getElementById('endpointSelector');
        selector.innerHTML = '<option value="">选择要测试的API端点</option>';
        
        Object.keys(this.config.endpoints).forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = this.getCategoryDisplayName(category);
            
            Object.keys(this.config.endpoints[category]).forEach(endpoint => {
                const option = document.createElement('option');
                option.value = `${category}.${endpoint}`;
                option.textContent = `${this.config.endpoints[category][endpoint].method} ${this.config.endpoints[category][endpoint].path} - ${this.config.endpoints[category][endpoint].description}`;
                optgroup.appendChild(option);
            });
            
            selector.appendChild(optgroup);
        });
    }

    getCategoryDisplayName(category) {
        const displayNames = {
            'authentication': '🔐 认证与授权',
            'users': '👤 用户管理',
            'uploads': '📤 文件上传',
            'characters': '🎭 角色管理',
            'tasks': '⚙️ 任务管理',
            'motions': '🎬 动作库'
        };
        return displayNames[category] || category;
    }

    bindEvents() {
        // 单个API测试
        document.getElementById('testSingleBtn').addEventListener('click', () => {
            this.testSelectedEndpoint();
        });

        // 批量测试
        document.getElementById('testAllBtn').addEventListener('click', () => {
            this.testAllEndpoints();
        });

        // 清除结果
        document.getElementById('clearResultsBtn').addEventListener('click', () => {
            this.clearResults();
        });

        // 导出结果
        document.getElementById('exportResultsBtn').addEventListener('click', () => {
            this.exportResults();
        });

        // 端点选择变化
        document.getElementById('endpointSelector').addEventListener('change', (e) => {
            this.onEndpointSelectionChange(e.target.value);
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
    }

    async testSelectedEndpoint() {
        const selector = document.getElementById('endpointSelector');
        const selectedValue = selector.value;
        
        if (!selectedValue) {
            this.showError('请先选择要测试的API端点');
            return;
        }

        const [category, endpoint] = selectedValue.split('.');
        await this.testSingleEndpoint(category, endpoint);
    }

    async testSingleEndpoint(category, endpoint) {
        if (this.isRunning) {
            this.showError('测试正在进行中，请等待完成');
            return;
        }

        this.isRunning = true;
        this.showStatus(`正在测试 ${category}.${endpoint}...`, 'info');

        try {
            const endpointConfig = this.config.endpoints[category][endpoint];
            const result = await this.executeTest(category, endpoint, endpointConfig);
            
            this.testResults.push(result);
            this.displayTestResult(result);
            
            this.showStatus(`测试完成: ${result.success ? '成功' : '失败'}`, result.success ? 'success' : 'error');
            
        } catch (error) {
            console.error('测试执行失败:', error);
            this.showError('测试执行失败: ' + error.message);
        } finally {
            this.isRunning = false;
        }
    }

    async executeTest(category, endpoint, config) {
        const startTime = Date.now();
        const testResult = {
            category,
            endpoint,
            method: config.method,
            path: config.path,
            description: config.description,
            timestamp: new Date().toISOString(),
            success: false,
            responseTime: 0,
            statusCode: null,
            response: null,
            error: null
        };

        try {
            // 构建请求URL
            let url = `${this.config.baseUrl}${config.path}`;
            
            // 处理路径参数（如果有的话，使用测试数据或提示用户输入）
            if (config.pathParams) {
                url = this.processPathParams(url, config.pathParams);
            }

            // 构建请求选项
            const requestOptions = {
                method: config.method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // 添加认证头
            if (config.requiresAuth && this.jwtToken) {
                requestOptions.headers['Authorization'] = `Bearer ${this.jwtToken}`;
            }

            // 添加请求体
            if (config.requestBody && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
                const testData = this.getTestData(config);
                if (testData) {
                    requestOptions.body = JSON.stringify(testData);
                }
            }

            // 添加查询参数
            if (config.queryParams && config.method === 'GET') {
                const queryParams = this.getQueryParams(config);
                if (queryParams) {
                    url += '?' + new URLSearchParams(queryParams).toString();
                }
            }

            // 发送请求
            const response = await fetch(url, requestOptions);
            const responseData = await response.json();
            
            testResult.responseTime = Date.now() - startTime;
            testResult.statusCode = response.status;
            testResult.response = responseData;
            testResult.success = response.ok;
            
            if (!response.ok) {
                testResult.error = responseData.error || responseData.message || 'Unknown error';
            }

        } catch (error) {
            testResult.responseTime = Date.now() - startTime;
            testResult.error = error.message;
            testResult.success = false;
        }

        return testResult;
    }

    processPathParams(url, pathParams) {
        // 简单处理：使用固定的测试UUID或从现有数据中获取
        Object.keys(pathParams).forEach(param => {
            if (param.includes('id')) {
                // 使用测试UUID
                url = url.replace(`{${param}}`, '550e8400-e29b-41d4-a716-************');
            }
        });
        return url;
    }

    getTestData(config) {
        if (config.testData && config.testData.valid) {
            return config.testData.valid;
        }
        return null;
    }

    getQueryParams(config) {
        if (config.queryParams) {
            // 返回默认的查询参数
            const params = {};
            Object.keys(config.queryParams).forEach(param => {
                if (param === 'page') params[param] = 1;
                if (param === 'page_size') params[param] = 10;
            });
            return params;
        }
        return null;
    }

    displayTestResult(result) {
        const resultsContainer = document.getElementById('testResults');
        const resultElement = document.createElement('div');
        resultElement.className = `test-result ${result.success ? 'success' : 'failure'}`;
        
        resultElement.innerHTML = `
            <div class="result-header">
                <span class="result-status">${result.success ? '✅' : '❌'}</span>
                <span class="result-title">${result.method} ${result.path}</span>
                <span class="result-time">${result.responseTime}ms</span>
                <span class="result-code">HTTP ${result.statusCode || 'N/A'}</span>
            </div>
            <div class="result-description">${result.description}</div>
            <div class="result-details">
                <div class="result-timestamp">测试时间: ${new Date(result.timestamp).toLocaleString()}</div>
                ${result.error ? `<div class="result-error">错误: ${result.error}</div>` : ''}
                <details class="result-response">
                    <summary>响应详情</summary>
                    <pre>${JSON.stringify(result.response, null, 2)}</pre>
                </details>
            </div>
        `;
        
        resultsContainer.insertBefore(resultElement, resultsContainer.firstChild);
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusMessage');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }

    showError(message) {
        this.showStatus(message, 'error');
    }

    logout() {
        this.jwtToken = null;
        this.currentUser = null;
        
        document.getElementById('loginStatus').className = 'status info';
        document.getElementById('loginStatus').textContent = '未登录 - 请先进行 Google 登录';
        document.getElementById('googleLogin').style.display = 'block';
        document.getElementById('userInfo').style.display = 'none';
        document.getElementById('testingArea').style.display = 'none';
        
        this.showStatus('已退出登录', 'info');
    }

    clearResults() {
        this.testResults = [];
        document.getElementById('testResults').innerHTML = '';
        this.showStatus('测试结果已清除', 'info');
    }

    async testAllEndpoints() {
        if (this.isRunning) {
            this.showError('测试正在进行中，请等待完成');
            return;
        }

        this.isRunning = true;
        this.showStatus('开始批量测试所有API端点...', 'info');

        const allEndpoints = [];

        // 收集所有端点
        Object.keys(this.config.endpoints).forEach(category => {
            Object.keys(this.config.endpoints[category]).forEach(endpoint => {
                allEndpoints.push({ category, endpoint });
            });
        });

        let completed = 0;
        const total = allEndpoints.length;

        try {
            for (const { category, endpoint } of allEndpoints) {
                this.showStatus(`正在测试 ${category}.${endpoint} (${completed + 1}/${total})...`, 'info');

                const endpointConfig = this.config.endpoints[category][endpoint];
                const result = await this.executeTest(category, endpoint, endpointConfig);

                this.testResults.push(result);
                this.displayTestResult(result);

                completed++;

                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            const successCount = this.testResults.slice(-total).filter(r => r.success).length;
            const failureCount = total - successCount;

            this.showStatus(`批量测试完成！成功: ${successCount}, 失败: ${failureCount}`, 'success');

        } catch (error) {
            console.error('批量测试失败:', error);
            this.showError('批量测试失败: ' + error.message);
        } finally {
            this.isRunning = false;
        }
    }

    exportResults() {
        if (this.testResults.length === 0) {
            this.showError('没有测试结果可导出');
            return;
        }

        const data = {
            exportTime: new Date().toISOString(),
            totalTests: this.testResults.length,
            successCount: this.testResults.filter(r => r.success).length,
            failureCount: this.testResults.filter(r => !r.success).length,
            results: this.testResults
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `mokta-api-test-results-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showStatus('测试结果已导出', 'success');
    }

    onEndpointSelectionChange(value) {
        if (!value) return;

        const [category, endpoint] = value.split('.');
        const config = this.config.endpoints[category][endpoint];
        
        // 显示端点详情
        this.displayEndpointDetails(config);
    }

    displayEndpointDetails(config) {
        const detailsContainer = document.getElementById('endpointDetails');
        
        let html = `
            <h4>API端点详情</h4>
            <p><strong>方法:</strong> ${config.method}</p>
            <p><strong>路径:</strong> ${config.path}</p>
            <p><strong>描述:</strong> ${config.description}</p>
            <p><strong>需要认证:</strong> ${config.requiresAuth ? '是' : '否'}</p>
        `;

        if (config.requestBody) {
            html += `
                <h5>请求体结构:</h5>
                <pre>${JSON.stringify(config.requestBody, null, 2)}</pre>
            `;
        }

        if (config.queryParams) {
            html += `
                <h5>查询参数:</h5>
                <pre>${JSON.stringify(config.queryParams, null, 2)}</pre>
            `;
        }

        if (config.expectedResponse) {
            html += `
                <h5>预期响应结构:</h5>
                <pre>${JSON.stringify(config.expectedResponse, null, 2)}</pre>
            `;
        }

        detailsContainer.innerHTML = html;
        detailsContainer.style.display = 'block';
    }
}

// 初始化API测试程序
document.addEventListener('DOMContentLoaded', () => {
    window.apiTester = new MoktaAPITester();
});
