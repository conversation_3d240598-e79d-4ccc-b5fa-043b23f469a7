# 🚀 部署Debug工具到Sealos开发服务器

## 📋 部署步骤

### 1. 确保debug目录已创建
```bash
# 在项目根目录运行部署脚本
python3 deploy_debug_tools.py
```

### 2. 检查Django配置
确保 `mokta/urls.py` 包含debug路由：
```python
# 开发环境下启用debug工具
if settings.DEBUG:
    urlpatterns += [
        path('debug/', include('debug.urls')),
    ]
```

### 3. 上传到开发服务器
将整个项目（包括debug目录）上传到Sealos开发服务器。

### 4. 在开发服务器上启动Django
```bash
# 激活conda环境
conda activate mokta-backend

# 启动Django服务器（端口8080会自动映射到外网）
python manage.py runserver 0.0.0.0:8080
```

## 🌐 访问地址

### 开发服务器访问
- **Debug工具首页**: https://pqqjrlgoetaa.usw.sealos.io/debug/
- **API测试工具（自动）**: https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/
- **API测试工具（手动）**: https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/manual/
- **前端测试页面**: https://pqqjrlgoetaa.usw.sealos.io/debug/frontend-test/

### 本地测试访问
- **Debug工具首页**: http://localhost:8000/debug/
- **API测试工具（自动）**: http://localhost:8000/debug/api-tester/
- **API测试工具（手动）**: http://localhost:8000/debug/api-tester/manual/
- **前端测试页面**: http://localhost:8000/debug/frontend-test/

## ✅ 优势确认

### 1. 🔗 OAuth重定向问题解决
- ✅ 使用统一域名 `pqqjrlgoetaa.usw.sealos.io`
- ✅ 重定向URI: `https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/`
- ✅ Google OAuth正常工作

### 2. 🚀 端口冲突问题解决
- ✅ 使用Django的8080端口（自动映射到443）
- ✅ 无需额外端口配置
- ✅ 通过子路径访问，避免端口冲突

### 3. 🛠️ 开发体验优化
- ✅ 统一的访问入口
- ✅ 自动环境检测和配置
- ✅ 无需启动额外服务

## 🔧 配置说明

### 环境自动检测
程序会根据域名自动检测环境：
- `pqqjrlgoetaa.usw.sealos.io` → 开发环境
- `localhost` → 本地环境

### API配置自动调整
- **开发环境**: API基础URL = `https://pqqjrlgoetaa.usw.sealos.io`
- **本地环境**: API基础URL = `http://localhost:8000`

### OAuth配置自动调整
- **开发环境**: 重定向URI = `https://pqqjrlgoetaa.usw.sealos.io/debug/api-tester/`
- **本地环境**: 重定向URI = `http://localhost:8000/debug/api-tester/`

## 📂 文件结构

```
mokta-backend/
├── debug/                          # Debug工具目录
│   ├── __init__.py                 # Django应用初始化
│   ├── apps.py                     # Django应用配置
│   ├── urls.py                     # URL路由配置
│   ├── views.py                    # Django视图
│   ├── templates/debug/            # Django模板
│   │   └── index.html              # Debug首页模板
│   ├── api-tester/                 # API测试工具
│   │   ├── index.html              # 自动化测试页面
│   │   ├── manual.html             # 手动测试页面
│   │   ├── js/                     # JavaScript文件
│   │   ├── css/                    # 样式文件
│   │   └── config/                 # 配置文件
│   └── frontend-test/              # 前端测试页面
│       ├── index.html
│       ├── app.js
│       └── style.css
├── mokta/
│   └── urls.py                     # 主URL配置（已添加debug路由）
├── deploy_debug_tools.py           # 部署脚本
├── test_debug_server.py            # 本地测试服务器
└── DEPLOY_TO_SEALOS.md            # 本文档
```

## 🐛 故障排除

### 1. 404错误
**问题**: 访问debug路径返回404
**解决方案**:
- 检查Django的URL配置是否正确
- 确认debug目录和文件是否存在
- 检查Django是否在DEBUG模式下运行

### 2. 静态文件无法加载
**问题**: CSS/JS文件加载失败
**解决方案**:
- 检查debug/views.py中的静态文件服务逻辑
- 确认文件路径是否正确
- 查看Django日志中的错误信息

### 3. OAuth重定向错误
**问题**: Google登录重定向失败
**解决方案**:
- 确认Google Cloud Console中的重定向URI配置
- 检查环境检测逻辑是否正确
- 验证域名和路径是否匹配

### 4. API请求失败
**问题**: API测试工具无法连接后端
**解决方案**:
- 检查后端API服务是否正常运行
- 确认API基础URL配置是否正确
- 检查CORS配置是否允许debug域名

## 📝 维护说明

### 更新debug工具
```bash
# 重新运行部署脚本
python3 deploy_debug_tools.py
```

### 同步最新代码
```bash
# 如果api-tester或frontend-test有更新
cp -r api-tester debug/
cp -r frontend-test debug/

# 重命名手动测试文件
mv debug/api-tester/manual-tester.html debug/api-tester/manual.html
```

### 检查部署状态
访问: https://pqqjrlgoetaa.usw.sealos.io/debug/status/

## 🎯 使用建议

1. **开发阶段**: 使用本地测试服务器进行快速开发
2. **集成测试**: 部署到开发服务器进行完整功能测试
3. **OAuth测试**: 在开发服务器上测试真实的Google OAuth流程
4. **API测试**: 使用手动测试工具进行详细的API调试

---

这种部署方式完美解决了您提到的OAuth重定向和端口冲突问题，同时提供了统一的访问体验。
