#!/usr/bin/env python3
"""
简单的测试服务器，用于测试debug工具
跳过数据库连接，直接服务静态文件
"""

import os
import sys
import mimetypes
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import json

class DebugHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        print(f"请求路径: {path}")
        
        # 处理debug路由
        if path.startswith('/debug/'):
            self.handle_debug_request(path)
        else:
            self.send_error(404, "Not Found")
    
    def handle_debug_request(self, path):
        """处理debug请求"""
        # 移除/debug/前缀
        debug_path = path[7:]  # 移除'/debug/'
        
        if debug_path == '' or debug_path == '/':
            # Debug首页
            self.serve_debug_index()
        elif debug_path == 'status/':
            # API状态
            self.serve_api_status()
        elif debug_path.startswith('api-tester/'):
            # API测试工具
            self.serve_api_tester(debug_path[11:])  # 移除'api-tester/'
        elif debug_path.startswith('frontend-test/'):
            # 前端测试
            self.serve_frontend_test(debug_path[14:])  # 移除'frontend-test/'
        else:
            self.send_error(404, f"Debug path not found: {debug_path}")
    
    def serve_debug_index(self):
        """服务debug首页"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>🐛 Mokta Debug 工具集合</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .tool-card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .tool-card h3 { color: #333; margin-top: 0; }
        .tool-card a { color: #007bff; text-decoration: none; font-weight: bold; }
        .tool-card a:hover { text-decoration: underline; }
        .status { background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Mokta Debug 工具集合</h1>
        
        <div class="status">
            <strong>🌍 当前环境:</strong> 测试服务器<br>
            <strong>📡 API地址:</strong> http://localhost:8000<br>
            <strong>🔗 访问地址:</strong> http://localhost:8000/debug/
        </div>
        
        <div class="tool-card">
            <h3>🧪 API测试工具（自动化）</h3>
            <p>基于API文档的自动化测试，支持批量测试所有端点</p>
            <a href="/debug/api-tester/">→ 打开自动化API测试工具</a>
        </div>
        
        <div class="tool-card">
            <h3>🛠️ API测试工具（手动）</h3>
            <p>类似Postman的手动API测试工具，支持完全自定义请求</p>
            <a href="/debug/api-tester/manual/">→ 打开手动API测试工具</a>
        </div>
        
        <div class="tool-card">
            <h3>📱 前端测试页面</h3>
            <p>完整的前端功能测试页面，包括文件上传、角色生成等</p>
            <a href="/debug/frontend-test/">→ 打开前端测试页面</a>
        </div>
        
        <div class="tool-card">
            <h3>📊 API状态检查</h3>
            <p>检查API服务状态和配置信息</p>
            <a href="/debug/status/">→ 查看API状态</a>
        </div>
    </div>
</body>
</html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_api_status(self):
        """服务API状态"""
        status = {
            'status': 'ok',
            'environment': 'test_server',
            'api_base_url': 'http://localhost:8000/api/v1/',
            'debug_tools': {
                'api_tester_auto': 'http://localhost:8000/debug/api-tester/',
                'api_tester_manual': 'http://localhost:8000/debug/api-tester/manual/',
                'frontend_test': 'http://localhost:8000/debug/frontend-test/',
            },
            'oauth_config': {
                'google_client_id': '930813472052-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com',
                'redirect_uri': 'http://localhost:8000/debug/api-tester/',
            }
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status, indent=2).encode('utf-8'))
    
    def serve_api_tester(self, sub_path):
        """服务API测试工具"""
        if sub_path == '' or sub_path == '/':
            # 主页
            file_path = 'debug/api-tester/index.html'
        elif sub_path == 'manual/' or sub_path == 'manual':
            # 手动测试页面
            file_path = 'debug/api-tester/manual.html'
        else:
            # 静态资源
            file_path = f'debug/api-tester/{sub_path}'
        
        self.serve_static_file(file_path)
    
    def serve_frontend_test(self, sub_path):
        """服务前端测试"""
        if sub_path == '' or sub_path == '/':
            file_path = 'debug/frontend-test/index.html'
        else:
            file_path = f'debug/frontend-test/{sub_path}'
        
        self.serve_static_file(file_path)
    
    def serve_static_file(self, file_path):
        """服务静态文件"""
        full_path = Path(file_path)
        
        if not full_path.exists():
            self.send_error(404, f"File not found: {file_path}")
            return
        
        try:
            with open(full_path, 'rb') as f:
                content = f.read()
            
            # 确定MIME类型
            content_type, _ = mimetypes.guess_type(str(full_path))
            if content_type is None:
                content_type = 'application/octet-stream'
            
            # 对HTML文件注入环境配置
            if content_type == 'text/html':
                content = self.inject_environment_config(content.decode('utf-8'))
                content = content.encode('utf-8')
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            self.send_error(500, f"Error reading file: {e}")
    
    def inject_environment_config(self, html_content):
        """注入环境配置"""
        config_script = """
    <script>
        // 自动注入的环境配置
        window.DEBUG_CONFIG = {
            apiBaseUrl: 'http://localhost:8000/api/v1/',
            currentDomain: 'localhost:8000',
            isDebugMode: true,
            oauthRedirectUri: 'http://localhost:8000/debug/api-tester/',
            environment: 'local'
        };
        
        // 覆盖环境检测函数
        function detectEnvironment() {
            return 'local';
        }
        
        function getApiBaseUrl() {
            return 'http://localhost:8000';
        }
        
        console.log('Debug环境配置已注入:', window.DEBUG_CONFIG);
    </script>
        """
        
        if '</head>' in html_content:
            html_content = html_content.replace('</head>', config_script + '\n</head>')
        else:
            html_content = config_script + '\n' + html_content
        
        return html_content

def main():
    port = 8000
    server_address = ('', port)
    
    print(f"🚀 启动Debug测试服务器")
    print(f"🌐 端口: {port}")
    print(f"🔗 访问地址: http://localhost:{port}/debug/")
    print("=" * 50)
    print("📋 可用工具:")
    print(f"  - Debug首页: http://localhost:{port}/debug/")
    print(f"  - API测试工具（自动）: http://localhost:{port}/debug/api-tester/")
    print(f"  - API测试工具（手动）: http://localhost:{port}/debug/api-tester/manual/")
    print(f"  - 前端测试页面: http://localhost:{port}/debug/frontend-test/")
    print(f"  - API状态: http://localhost:{port}/debug/status/")
    print("=" * 50)
    print("🛑 按 Ctrl+C 停止服务器")
    
    try:
        httpd = HTTPServer(server_address, DebugHandler)
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()
